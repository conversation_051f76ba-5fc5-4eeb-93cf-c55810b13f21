#!/usr/bin/env python3
"""
Script de nettoyage pour supprimer les anciens fichiers après réorganisation
"""

import os
import shutil

def cleanup_old_files():
    """Supprime les anciens fichiers maintenant réorganisés"""
    
    old_files = [
        'generate_enhanced_prompt.py',
        'generate_analysis.py', 
        'arc_prompt_generator4.py',
        'extract_key_insights.py',
        'validate_generic_insights.py',
        'test_complete_workflow.py',
        'test_enhanced_prompt.py',
        'README_enhanced_prompts.md',
        'SUMMARY_enhanced_system.md'
    ]
    
    print("🧹 Nettoyage des anciens fichiers...")
    
    removed_count = 0
    for file in old_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"✅ Supprimé: {file}")
                removed_count += 1
            except Exception as e:
                print(f"❌ Erreur lors de la suppression de {file}: {e}")
        else:
            print(f"⚠️  Déjà supprimé: {file}")
    
    print(f"\n🎯 Nettoyage terminé: {removed_count} fichiers supprimés")
    print("📁 Nouvelle structure organisée dans:")
    print("   - scripts/core/     (scripts principaux)")
    print("   - scripts/utils/    (utilitaires)")
    print("   - scripts/tests/    (tests)")
    print("   - docs/             (documentation)")
    print("\n🚀 Utilisez maintenant: python arc_enhanced_prompt.py")

if __name__ == "__main__":
    cleanup_old_files()