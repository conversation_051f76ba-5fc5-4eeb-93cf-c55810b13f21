#!/usr/bin/env python3
"""
Script de validation pour s'assurer que les insights sont génériques
et ne contiennent pas d'informations hardcodées spécifiques à un puzzle
"""

import os
import json
import glob
from .extract_key_insights import extract_key_insights

def validate_insights_are_generic():
    """Valide que les insights sont basés uniquement sur l'analyse automatique"""
    
    print("🔍 Validation de la généricité des insights...")
    
    # Mots-clés interdits (hardcodés/spécifiques)
    forbidden_keywords = [
        "distance Manhattan",  # Trop spécifique
        "bordure",  # Terme hardcodé
        "taille ≥10",  # Valeur hardcodée
        "déplacement vers le bas",  # Spécifique à un puzzle
        "proximité aux bordures"  # Trop spécifique
    ]
    
    # Chercher tous les fichiers d'analyse
    analysis_files = glob.glob("analysis_data/training/*_analysis.json")
    
    if not analysis_files:
        print("❌ Aucun fichier d'analyse trouvé")
        return False
    
    print(f"📁 Trouvé {len(analysis_files)} fichiers d'analyse")
    
    validation_passed = True
    
    for analysis_file in analysis_files[:3]:  # Tester les 3 premiers
        puzzle_id = os.path.basename(analysis_file).replace("_analysis.json", "")
        print(f"\n🧪 Test du puzzle {puzzle_id}...")
        
        try:
            with open(analysis_file, 'r') as f:
                analysis_data = json.load(f)
            
            # Extraire les insights
            insights = extract_key_insights(analysis_data)
            
            # Vérifier qu'il n'y a pas de mots-clés interdits
            insights_text = json.dumps(insights, ensure_ascii=False).lower()
            
            found_forbidden = []
            for keyword in forbidden_keywords:
                if keyword.lower() in insights_text:
                    found_forbidden.append(keyword)
            
            if found_forbidden:
                print(f"❌ Mots-clés interdits trouvés: {found_forbidden}")
                validation_passed = False
            else:
                print(f"✅ Insights génériques validés")
            
            # Vérifier que les observations sont basées sur les données
            observations = insights.get("critical_observations", [])
            print(f"📊 {len(observations)} observations générées")
            
            # Vérifier la structure des données
            required_sections = ["color_transformations", "object_structure", "transformation_type"]
            for section in required_sections:
                if section in insights:
                    print(f"✅ Section {section} présente")
                else:
                    print(f"⚠️  Section {section} manquante")
            
        except Exception as e:
            print(f"❌ Erreur lors du traitement de {puzzle_id}: {e}")
            validation_passed = False
    
    return validation_passed

def test_cross_contamination():
    """Teste qu'il n'y a pas de contamination croisée entre puzzles"""
    
    print("\n🔬 Test de contamination croisée...")
    
    analysis_files = glob.glob("analysis_data/training/*_analysis.json")[:2]
    
    if len(analysis_files) < 2:
        print("⚠️  Pas assez de fichiers pour tester la contamination croisée")
        return True
    
    insights_list = []
    
    for analysis_file in analysis_files:
        puzzle_id = os.path.basename(analysis_file).replace("_analysis.json", "")
        
        with open(analysis_file, 'r') as f:
            analysis_data = json.load(f)
        
        insights = extract_key_insights(analysis_data)
        insights_list.append((puzzle_id, insights))
    
    # Comparer les insights entre puzzles
    puzzle1_id, puzzle1_insights = insights_list[0]
    puzzle2_id, puzzle2_insights = insights_list[1]
    
    # Les insights doivent être différents (pas de copie)
    obs1 = set(puzzle1_insights.get("critical_observations", []))
    obs2 = set(puzzle2_insights.get("critical_observations", []))
    
    if obs1 == obs2:
        print(f"❌ Observations identiques entre {puzzle1_id} et {puzzle2_id} - possible contamination")
        return False
    else:
        print(f"✅ Observations différentes entre {puzzle1_id} et {puzzle2_id}")
        return True

def main():
    """Fonction principale de validation"""
    
    print("🛡️  VALIDATION DE LA GÉNÉRICITÉ DES INSIGHTS")
    print("=" * 50)
    
    # Test 1: Vérifier que les insights sont génériques
    generic_ok = validate_insights_are_generic()
    
    # Test 2: Vérifier qu'il n'y a pas de contamination croisée
    contamination_ok = test_cross_contamination()
    
    print("\n" + "=" * 50)
    print("📋 RÉSULTATS DE LA VALIDATION:")
    
    if generic_ok:
        print("✅ Insights génériques: VALIDÉ")
    else:
        print("❌ Insights génériques: ÉCHEC")
    
    if contamination_ok:
        print("✅ Pas de contamination croisée: VALIDÉ")
    else:
        print("❌ Contamination croisée détectée: ÉCHEC")
    
    overall_success = generic_ok and contamination_ok
    
    if overall_success:
        print("\n🎉 VALIDATION RÉUSSIE - Le système est générique et sûr")
        return 0
    else:
        print("\n⚠️  VALIDATION ÉCHOUÉE - Corrections nécessaires")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())