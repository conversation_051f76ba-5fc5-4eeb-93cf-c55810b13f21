#!/usr/bin/env python3
"""
Tests pour l'orchestrateur principal AutomatedARCAnalyzer.

Ce module teste les fonctionnalités principales de l'analyseur automatisé
incluant l'initialisation, la configuration et les méthodes utilitaires.
"""

import unittest
import sys
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import numpy as np
from datetime import datetime

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from core.automated_analyzer import AutomatedARCAnalyzer
from core.data_models import AnalysisResult, ValidationResult, ParsedResponse, PuzzleData
from core.constants import ChatSessionTypes, ExitCodes


class TestAutomatedARCAnalyzer(unittest.TestCase):
    """Tests pour la classe AutomatedARCAnalyzer."""
    
    @patch('core.automated_analyzer.SecurePuzzleLoader')
    def setUp(self, mock_loader_class):
        """Configuration des tests."""
        # Mock le puzzle loader pour éviter la validation du répertoire
        mock_loader = Mock()
        mock_loader_class.return_value = mock_loader
        
        self.config = {
            'arc_data_dir': 'test_data',
            'analysis_dir': 'test_analysis',
            'results_dir': 'test_results',
            'show_progress': False,
            'log_level': 'ERROR'  # Réduire le bruit pendant les tests
        }
        self.analyzer = AutomatedARCAnalyzer(self.config)
    
    def test_initialization(self):
        """Test l'initialisation de l'analyseur."""
        self.assertIsNotNone(self.analyzer.puzzle_loader)
        self.assertIsNotNone(self.analyzer.arc_analyzer)
        self.assertIsNotNone(self.analyzer.response_parser)
        self.assertIsNotNone(self.analyzer.solution_validator)
        self.assertIsNotNone(self.analyzer.learning_system)
        
        # Vérifier la configuration
        self.assertEqual(self.analyzer.chat_config.session_type, ChatSessionTypes.BLIND_ANALYSIS)
        self.assertFalse(self.analyzer.show_progress)
    
    @patch('core.automated_analyzer.SecurePuzzleLoader')
    def test_default_initialization(self, mock_loader_class):
        """Test l'initialisation avec configuration par défaut."""
        mock_loader = Mock()
        mock_loader_class.return_value = mock_loader
        
        default_analyzer = AutomatedARCAnalyzer()
        self.assertIsNotNone(default_analyzer.puzzle_loader)
        self.assertTrue(default_analyzer.enable_learning)
        self.assertTrue(default_analyzer.save_success)
    
    def test_format_grid_visual(self):
        """Test le formatage visuel des grilles."""
        # Grille test simple
        test_grid = np.array([[0, 1, 2], [3, 4, 5]])
        
        formatted = self.analyzer._format_grid_visual(test_grid)
        
        # Vérifier que le formatage contient les symboles et les valeurs numériques
        self.assertIn('⬛', formatted)  # Noir (0)
        self.assertIn('🟦', formatted)  # Bleu (1)
        self.assertIn('[0,1,2]', formatted)
        self.assertIn('[3,4,5]', formatted)
    
    def test_format_grid_visual_none(self):
        """Test le formatage avec grille None."""
        formatted = self.analyzer._format_grid_visual(None)
        self.assertEqual(formatted, "Grille non disponible")
    
    def test_format_diagnostic_grid(self):
        """Test le formatage de la grille de diagnostic."""
        diagnostic_grid = np.array([['T', 'F', 'T'], ['F', 'T', 'F']])
        
        formatted = self.analyzer._format_diagnostic_grid(diagnostic_grid)
        
        lines = formatted.split('\n')
        self.assertEqual(len(lines), 2)
        self.assertEqual(lines[0], 'T F T')
        self.assertEqual(lines[1], 'F T F')
    
    def test_format_grid_basic(self):
        """Test le formatage basique de grille."""
        # Test avec liste
        grid_list = [[0, 1], [2, 3]]
        formatted = self.analyzer._format_grid(grid_list)
        self.assertIn('[0, 1]', formatted)
        self.assertIn('[2, 3]', formatted)
        
        # Test avec numpy array
        grid_array = np.array([[0, 1], [2, 3]])
        formatted = self.analyzer._format_grid(grid_array)
        self.assertIn('[0, 1]', formatted)
        self.assertIn('[2, 3]', formatted)
    
    def test_get_analysis_summary(self):
        """Test la génération du résumé d'analyse."""
        summary = self.analyzer.get_analysis_summary()
        
        # Vérifier la structure du résumé
        self.assertIn('analyzer_version', summary)
        self.assertIn('components', summary)
        self.assertIn('configuration', summary)
        
        # Vérifier les composants
        components = summary['components']
        self.assertIn('puzzle_loader', components)
        self.assertIn('chat_session', components)
        self.assertIn('response_parser', components)
        self.assertIn('solution_validator', components)
        self.assertIn('learning_system', components)
        
        # Vérifier la configuration
        config = summary['configuration']
        self.assertIn('chat_session_type', config)
        self.assertIn('enable_learning', config)
        self.assertIn('save_success', config)
    
    def test_build_basic_prompt(self):
        """Test la construction d'un prompt basique."""
        # Créer des données de puzzle test
        puzzle_data = PuzzleData(
            puzzle_id="test_001",
            train_examples=[
                {'input': [[0, 1], [2, 3]], 'output': [[1, 0], [3, 2]]},
                {'input': [[4, 5], [6, 7]], 'output': [[5, 4], [7, 6]]}
            ],
            test_input=np.array([[8, 9], [0, 1]]),
            hidden_solution=None,
            subset="training"
        )
        
        prompt = self.analyzer._build_basic_prompt(puzzle_data)
        
        # Vérifier que le prompt contient les éléments essentiels
        self.assertIn("test_001", prompt)
        self.assertIn("Exemples d'Entraînement", prompt)
        self.assertIn("Grille Test", prompt)
        self.assertIn("Instructions", prompt)
        self.assertIn("[0, 1]", prompt)  # Données d'exemple
        self.assertIn("[8, 9]", prompt)  # Données de test
    
    @patch('core.automated_analyzer.extract_key_insights')
    def test_build_enhanced_prompt(self, mock_extract_insights):
        """Test la construction d'un prompt amélioré avec insights."""
        # Mock des insights
        mock_insights = {
            'critical_observations': [
                'Transformation de couleur détectée',
                'Pattern de réflexion horizontale'
            ]
        }
        
        # Créer des données de puzzle test
        puzzle_data = PuzzleData(
            puzzle_id="test_002",
            train_examples=[
                {'input': [[0, 1]], 'output': [[1, 0]]}
            ],
            test_input=np.array([[2, 3]]),
            hidden_solution=None,
            subset="training"
        )
        
        prompt = self.analyzer._build_enhanced_prompt(puzzle_data, mock_insights)
        
        # Vérifier que le prompt contient les insights
        self.assertIn("Insights Automatiques", prompt)
        self.assertIn("Transformation de couleur détectée", prompt)
        self.assertIn("Pattern de réflexion horizontale", prompt)
        self.assertIn("test_002", prompt)
    
    def test_format_successful_analysis(self):
        """Test le formatage d'une analyse réussie."""
        # Créer des données de test
        validation_result = ValidationResult(
            is_correct=True,
            accuracy_percentage=100.0,
            diagnostic_grid=np.array([['T', 'T'], ['T', 'T']]),
            total_errors=0,
            error_positions=[]
        )
        
        parsed_response = ParsedResponse(
            transformations=['Réflexion horizontale'],
            patterns=['Symétrie'],
            rules=['Si input[i][j] alors output[i][1-j]'],
            proposed_grid=np.array([[1, 0], [3, 2]]),
            reasoning_steps=['Identifier la transformation', 'Appliquer la règle'],
            interpretation='Réflexion des éléments',
            raw_response='Réponse complète de l\'IA'
        )
        
        analysis_result = AnalysisResult(
            puzzle_id="test_format",
            success=True,
            proposed_solution=np.array([[1, 0], [3, 2]]),
            validation_result=validation_result,
            ai_analysis=parsed_response,
            execution_time=2.5,
            saved_to=None,
            timestamp=datetime(2024, 1, 1, 12, 0, 0)
        )
        
        puzzle_data = PuzzleData(
            puzzle_id="test_format",
            train_examples=[{'input': [[0, 1]], 'output': [[1, 0]]}],
            test_input=np.array([[0, 1], [2, 3]]),
            hidden_solution=None,
            subset="training"
        )
        
        formatted = self.analyzer._format_successful_analysis(analysis_result, puzzle_data)
        
        # Vérifier les sections principales
        self.assertIn("=== ANALYSE PUZZLE test_format", formatted)
        self.assertIn("📊 MÉTADONNÉES", formatted)
        self.assertIn("🔍 TRANSFORMATIONS DÉTECTÉES", formatted)
        self.assertIn("🎯 PATTERNS IDENTIFIÉS", formatted)
        self.assertIn("📋 RÈGLES DÉDUITES", formatted)
        self.assertIn("🎲 GRILLE OUTPUT PROPOSÉE", formatted)
        self.assertIn("🧠 RAISONNEMENT ÉTAPE PAR ÉTAPE", formatted)
        self.assertIn("✅ VALIDATION", formatted)
        self.assertIn("SUCCÈS - Puzzle résolu !", formatted)
        self.assertIn("=== FIN ANALYSE ===", formatted)
        
        # Vérifier le contenu spécifique
        self.assertIn("Réflexion horizontale", formatted)
        self.assertIn("Symétrie", formatted)
        self.assertIn("Si input[i][j] alors output[i][1-j]", formatted)
        self.assertIn("Durée d'analyse: 2.5s", formatted)


class TestAutomatedAnalyzerIntegration(unittest.TestCase):
    """Tests d'intégration pour l'analyseur automatisé."""
    
    @patch('core.automated_analyzer.SecurePuzzleLoader')
    def setUp(self, mock_loader_class):
        """Configuration des tests d'intégration."""
        # Mock le puzzle loader
        mock_loader = Mock()
        mock_loader_class.return_value = mock_loader
        
        self.config = {
            'show_progress': False,
            'log_level': 'ERROR',
            'enable_learning': False  # Désactiver pour les tests
        }
        self.analyzer = AutomatedARCAnalyzer(self.config)
    
    def test_analyze_puzzle_workflow_mock(self):
        """Test du workflow complet avec mocks."""
        # Mock tous les composants nécessaires
        with patch.object(self.analyzer, 'puzzle_loader') as mock_loader, \
             patch('core.automated_analyzer.ChatSession') as mock_chat_class, \
             patch.object(self.analyzer, '_ensure_analysis_exists') as mock_ensure_analysis:
            
            # Mock puzzle data
            puzzle_data = PuzzleData(
                puzzle_id="mock_001",
                train_examples=[{'input': [[0, 1]], 'output': [[1, 0]]}],
                test_input=np.array([[2, 3]]),
                hidden_solution=None,
                subset="training"
            )
            hidden_solution = np.array([[3, 2]])
            
            mock_loader.load_puzzle_for_analysis.return_value = (puzzle_data, hidden_solution)
            
            # Mock analysis file
            mock_ensure_analysis.return_value = Path("mock_analysis.json")
            
            # Mock chat session
            mock_session = Mock()
            mock_chat_class.return_value.__enter__.return_value = mock_session
            mock_session.send_prompt.return_value = """
            Analyse du puzzle:
            
            TRANSFORMATIONS DÉTECTÉES:
            - Réflexion horizontale
            
            PATTERNS IDENTIFIÉS:
            - Symétrie
            
            RÈGLES DÉDUITES:
            - Inverser l'ordre des colonnes
            
            GRILLE OUTPUT PROPOSÉE:
            [[3,2]]
            
            RAISONNEMENT:
            1. Identifier la transformation
            2. Appliquer la règle
            """
            
            # Mock extract_key_insights
            with patch('core.automated_analyzer.extract_key_insights') as mock_extract:
                mock_extract.return_value = {'critical_observations': []}
                
                # Exécuter l'analyse
                result = self.analyzer.analyze_puzzle("mock_001")
                
                # Vérifications
                self.assertEqual(result.puzzle_id, "mock_001")
                self.assertIsNotNone(result.ai_analysis)
                self.assertIsNotNone(result.validation_result)
                
                # Vérifier que les mocks ont été appelés
                mock_loader.load_puzzle_for_analysis.assert_called_once_with("mock_001")
                mock_session.send_prompt.assert_called_once()


def run_tests():
    """Exécute tous les tests."""
    # Créer la suite de tests
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Ajouter les tests
    suite.addTests(loader.loadTestsFromTestCase(TestAutomatedARCAnalyzer))
    suite.addTests(loader.loadTestsFromTestCase(TestAutomatedAnalyzerIntegration))
    
    # Exécuter les tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)