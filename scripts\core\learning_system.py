#!/usr/bin/env python3
"""
Système d'apprentissage pour l'amélioration continue des capacités d'analyse ARC AGI.

Ce module implémente l'extraction et l'intégration d'insights génériques
à partir d'analyses réussies, avec validation stricte de la généricité.
"""

import json
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from utils.extract_key_insights import extract_key_insights, format_insights_for_prompt
from utils.validate_generic_insights import validate_insights_are_generic
from core.data_models import AnalysisResult, LearningInsight


class LearningSystem:
    """
    Système d'apprentissage pour l'amélioration continue des analyses ARC AGI.
    
    Ce système extrait des insights génériques à partir d'analyses réussies
    et les intègre dans le système pour améliorer les analyses futures.
    """
    
    def __init__(self):
        """Initialise le système d'apprentissage."""
        self.insights_cache = {}
        self.validation_enabled = True
        
    def propose_learning(self, analysis_result: AnalysisResult) -> bool:
        """
        Demande à l'utilisateur s'il souhaite extraire des insights de l'analyse réussie.
        
        Args:
            analysis_result: Résultat d'analyse réussie
            
        Returns:
            bool: True si l'utilisateur accepte l'extraction d'insights
        """
        if not analysis_result.success:
            print("⚠️  L'analyse n'a pas réussi - pas d'insights à extraire")
            return False
            
        print(f"\n🎉 Puzzle {analysis_result.puzzle_id} résolu avec succès!")
        print("💡 Le système peut extraire des insights de cette analyse réussie")
        print("   pour améliorer les analyses futures.")
        print("\nCes insights seront:")
        print("• Basés uniquement sur l'analyse automatique")
        print("• Validés pour leur généricité")
        print("• Intégrés sans contamination croisée")
        
        while True:
            response = input("\n❓ Voulez-vous extraire des insights? (o/n): ").strip().lower()
            if response in ['o', 'oui', 'y', 'yes']:
                return True
            elif response in ['n', 'non', 'no']:
                print("📝 Aucun insight extrait - analyse sauvegardée sans apprentissage")
                return False
            else:
                print("⚠️  Réponse non reconnue. Veuillez répondre par 'o' ou 'n'")
    
    def extract_and_integrate_insights(self, successful_analysis: Dict) -> bool:
        """
        Extrait et intègre des insights à partir d'une analyse réussie.
        
        Args:
            successful_analysis: Données complètes de l'analyse réussie
            
        Returns:
            bool: True si l'extraction et l'intégration ont réussi
        """
        try:
            puzzle_id = successful_analysis.get("puzzle_id", "unknown")
            print(f"\n🔍 Extraction d'insights pour le puzzle {puzzle_id}...")
            
            # 1. Extraire les insights de base
            raw_insights = self._extract_raw_insights(successful_analysis)
            if not raw_insights:
                print("❌ Aucun insight extrait - données insuffisantes")
                return False
            
            # 2. Valider la généricité des insights
            if not self._validate_insight_genericity(raw_insights, puzzle_id):
                print("❌ Validation de généricité échouée - insights rejetés")
                return False
            
            # 3. Transformer en insights structurés
            structured_insights = self._structure_insights(raw_insights, puzzle_id)
            
            # 4. Intégrer dans le système
            integration_success = self._integrate_insights(structured_insights)
            
            if integration_success:
                print(f"✅ Insights intégrés avec succès pour {puzzle_id}")
                print(f"📊 {len(structured_insights)} nouveaux insights ajoutés")
                return True
            else:
                print("❌ Échec de l'intégration des insights")
                return False
                
        except Exception as e:
            print(f"❌ Erreur lors de l'extraction d'insights: {e}")
            return False
    
    def _extract_raw_insights(self, successful_analysis: Dict) -> Optional[Dict]:
        """
        Extrait les insights bruts à partir des données d'analyse.
        
        Args:
            successful_analysis: Données d'analyse complètes
            
        Returns:
            Dict contenant les insights bruts ou None si échec
        """
        try:
            # Utiliser l'extracteur existant
            analysis_data = successful_analysis.get("analysis_data", {})
            if not analysis_data:
                print("⚠️  Données d'analyse manquantes")
                return None
            
            raw_insights = extract_key_insights(analysis_data)
            
            # Vérifier que des insights ont été extraits
            if not raw_insights.get("critical_observations"):
                print("⚠️  Aucune observation critique extraite")
                return None
            
            print(f"📋 {len(raw_insights.get('critical_observations', []))} observations extraites")
            return raw_insights
            
        except Exception as e:
            print(f"❌ Erreur lors de l'extraction brute: {e}")
            return None
    
    def _validate_insight_genericity(self, insights: Dict, puzzle_id: str) -> bool:
        """
        Valide que les insights sont génériques selon les règles arc_insights_safety.
        
        Args:
            insights: Insights à valider
            puzzle_id: ID du puzzle source
            
        Returns:
            bool: True si les insights sont génériques
        """
        if not self.validation_enabled:
            return True
            
        try:
            print("🛡️  Validation de la généricité des insights...")
            
            # Mots-clés interdits selon arc_insights_safety
            forbidden_keywords = [
                "distance manhattan",
                "bordure", "bordures",
                "taille ≥", "taille >=",
                "déplacement vers le bas",
                "proximité aux bordures",
                "position (", "position(",
                f"puzzle {puzzle_id}",  # Référence au puzzle spécifique
                "comme dans le puzzle"
            ]
            
            # Convertir les insights en texte pour validation
            insights_text = json.dumps(insights, ensure_ascii=False).lower()
            
            # Vérifier les mots-clés interdits
            found_forbidden = []
            for keyword in forbidden_keywords:
                if keyword.lower() in insights_text:
                    found_forbidden.append(keyword)
            
            if found_forbidden:
                print(f"❌ Mots-clés interdits détectés: {found_forbidden}")
                return False
            
            # Vérifier que les insights sont basés sur des données
            observations = insights.get("critical_observations", [])
            data_based_count = 0
            
            for obs in observations:
                # Les observations valides doivent contenir des données spécifiques
                if any(indicator in obs.lower() for indicator in [
                    "détecté", "analysé", "calculé", "extrait",
                    "occurrences", "pixels", "taille", "couleur"
                ]):
                    data_based_count += 1
            
            if data_based_count == 0:
                print("❌ Aucune observation basée sur les données détectée")
                return False
            
            print(f"✅ Validation réussie - {data_based_count} observations basées sur les données")
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors de la validation: {e}")
            return False
    
    def _structure_insights(self, raw_insights: Dict, puzzle_id: str) -> List[LearningInsight]:
        """
        Structure les insights bruts en objets LearningInsight.
        
        Args:
            raw_insights: Insights bruts extraits
            puzzle_id: ID du puzzle source
            
        Returns:
            Liste d'objets LearningInsight structurés
        """
        structured_insights = []
        timestamp = datetime.now()
        
        try:
            # Traiter les observations critiques
            observations = raw_insights.get("critical_observations", [])
            for obs in observations:
                # Déterminer le type d'insight
                insight_type = self._classify_insight_type(obs)
                
                # Extraire les conditions
                conditions = self._extract_conditions(obs, raw_insights)
                
                # Calculer la confiance basée sur les données
                confidence = self._calculate_confidence(obs, raw_insights)
                
                insight = LearningInsight(
                    insight_type=insight_type,
                    description=obs,
                    conditions=conditions,
                    confidence=confidence,
                    source_puzzle=puzzle_id,
                    extracted_at=timestamp
                )
                
                structured_insights.append(insight)
            
            print(f"📊 {len(structured_insights)} insights structurés créés")
            return structured_insights
            
        except Exception as e:
            print(f"❌ Erreur lors de la structuration: {e}")
            return []
    
    def _classify_insight_type(self, observation: str) -> str:
        """
        Classifie le type d'insight basé sur l'observation.
        
        Args:
            observation: Observation à classifier
            
        Returns:
            Type d'insight
        """
        obs_lower = observation.lower()
        
        if "couleur" in obs_lower or "color" in obs_lower:
            return "color_transformation"
        elif "structure" in obs_lower or "objet" in obs_lower:
            return "object_structure"
        elif "alignement" in obs_lower or "spatial" in obs_lower:
            return "spatial_pattern"
        elif "transformation" in obs_lower:
            return "transformation_type"
        elif "changement" in obs_lower or "pixel" in obs_lower:
            return "change_pattern"
        elif "motif" in obs_lower or "pattern" in obs_lower:
            return "motif_detection"
        else:
            return "general_observation"
    
    def _extract_conditions(self, observation: str, raw_insights: Dict) -> List[str]:
        """
        Extrait les conditions d'application de l'insight.
        
        Args:
            observation: Observation source
            raw_insights: Données d'insights complètes
            
        Returns:
            Liste des conditions d'application
        """
        conditions = []
        
        # Conditions basées sur les transformations de couleur
        if raw_insights.get("color_transformations", {}).get("total_transformations", 0) > 0:
            conditions.append("color_transformations_detected")
        
        # Conditions basées sur la structure des objets
        if raw_insights.get("object_structure", {}).get("size_variety", 0) > 1:
            conditions.append("multiple_object_sizes")
        
        # Conditions basées sur le taux de changement
        if raw_insights.get("change_statistics", {}).get("low_change_rate", False):
            conditions.append("low_change_rate")
        
        # Conditions basées sur les patterns spatiaux
        if raw_insights.get("spatial_patterns", {}).get("has_horizontal_borders", False):
            conditions.append("horizontal_alignment_detected")
        
        if raw_insights.get("spatial_patterns", {}).get("has_vertical_borders", False):
            conditions.append("vertical_alignment_detected")
        
        return conditions
    
    def _calculate_confidence(self, observation: str, raw_insights: Dict) -> float:
        """
        Calcule la confiance de l'insight basée sur les données.
        
        Args:
            observation: Observation source
            raw_insights: Données d'insights complètes
            
        Returns:
            Niveau de confiance entre 0 et 1
        """
        confidence = 0.5  # Confiance de base
        
        # Augmenter la confiance si basé sur des données quantitatives
        if any(indicator in observation.lower() for indicator in [
            "occurrences", "pixels", "pourcentage", "%", "taille"
        ]):
            confidence += 0.2
        
        # Augmenter si supporté par plusieurs types de données
        supporting_data_types = 0
        if raw_insights.get("color_transformations"):
            supporting_data_types += 1
        if raw_insights.get("object_structure"):
            supporting_data_types += 1
        if raw_insights.get("change_statistics"):
            supporting_data_types += 1
        
        confidence += min(supporting_data_types * 0.1, 0.3)
        
        return min(confidence, 1.0)
    
    def _integrate_insights(self, insights: List[LearningInsight]) -> bool:
        """
        Intègre les insights dans le système pour usage futur.
        
        Args:
            insights: Liste d'insights à intégrer
            
        Returns:
            bool: True si l'intégration a réussi
        """
        try:
            # Pour l'instant, sauvegarder dans un cache en mémoire
            # Dans une implémentation future, ceci pourrait être persisté
            for insight in insights:
                insight_key = f"{insight.insight_type}_{insight.source_puzzle}"
                self.insights_cache[insight_key] = insight
            
            print(f"💾 {len(insights)} insights intégrés dans le cache")
            
            # Optionnel: sauvegarder sur disque pour persistance
            self._save_insights_to_disk(insights)
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors de l'intégration: {e}")
            return False
    
    def _save_insights_to_disk(self, insights: List[LearningInsight]) -> None:
        """
        Sauvegarde les insights sur disque pour persistance.
        
        Args:
            insights: Insights à sauvegarder
        """
        try:
            insights_dir = Path("analysis_data/insights")
            insights_dir.mkdir(parents=True, exist_ok=True)
            
            for insight in insights:
                filename = f"insight_{insight.source_puzzle}_{insight.insight_type}.json"
                filepath = insights_dir / filename
                
                insight_data = {
                    "insight_type": insight.insight_type,
                    "description": insight.description,
                    "conditions": insight.conditions,
                    "confidence": insight.confidence,
                    "source_puzzle": insight.source_puzzle,
                    "extracted_at": insight.extracted_at.isoformat()
                }
                
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(insight_data, f, indent=2, ensure_ascii=False)
            
            print(f"💾 Insights sauvegardés dans {insights_dir}")
            
        except Exception as e:
            print(f"⚠️  Erreur lors de la sauvegarde: {e}")
    
    def get_applicable_insights(self, analysis_context: Dict) -> List[LearningInsight]:
        """
        Récupère les insights applicables pour un contexte d'analyse donné.
        
        Args:
            analysis_context: Contexte de l'analyse en cours
            
        Returns:
            Liste d'insights applicables
        """
        applicable_insights = []
        
        for insight in self.insights_cache.values():
            if self._is_insight_applicable(insight, analysis_context):
                applicable_insights.append(insight)
        
        # Trier par confiance décroissante
        applicable_insights.sort(key=lambda x: x.confidence, reverse=True)
        
        return applicable_insights
    
    def _is_insight_applicable(self, insight: LearningInsight, context: Dict) -> bool:
        """
        Détermine si un insight est applicable au contexte donné.
        
        Args:
            insight: Insight à évaluer
            context: Contexte d'analyse
            
        Returns:
            bool: True si l'insight est applicable
        """
        # Vérifier les conditions d'application
        for condition in insight.conditions:
            if not self._check_condition(condition, context):
                return False
        
        return True
    
    def _check_condition(self, condition: str, context: Dict) -> bool:
        """
        Vérifie si une condition est remplie dans le contexte donné.
        
        Args:
            condition: Condition à vérifier
            context: Contexte d'analyse
            
        Returns:
            bool: True si la condition est remplie
        """
        # Implémentation basique - à étendre selon les besoins
        if condition == "color_transformations_detected":
            return context.get("has_color_transformations", False)
        elif condition == "multiple_object_sizes":
            return context.get("object_size_variety", 0) > 1
        elif condition == "low_change_rate":
            return context.get("change_percentage", 100) < 10
        
        return False


def main():
    """Fonction principale pour tester le système d'apprentissage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Système d\'apprentissage ARC AGI')
    parser.add_argument('--test', action='store_true', help='Exécuter les tests')
    parser.add_argument('--validate', action='store_true', help='Valider la généricité')
    
    args = parser.parse_args()
    
    learning_system = LearningSystem()
    
    if args.validate:
        print("🛡️  Validation de la généricité des insights...")
        success = validate_insights_are_generic()
        if success:
            print("✅ Validation réussie")
        else:
            print("❌ Validation échouée")
        return 0 if success else 1
    
    if args.test:
        print("🧪 Test du système d'apprentissage...")
        # Test basique
        test_analysis = {
            "puzzle_id": "test_001",
            "analysis_data": {
                "raw_analysis": {
                    "transformations": {"color": {"color_removal": True}},
                    "diff_analysis": {
                        "common_color_changes": {"3→4": 5},
                        "train_diffs": [{"change_percentage": 5.2}]
                    }
                }
            }
        }
        
        insights = learning_system._extract_raw_insights(test_analysis)
        if insights:
            print("✅ Extraction d'insights réussie")
            print(f"📊 {len(insights.get('critical_observations', []))} observations")
        else:
            print("❌ Échec de l'extraction d'insights")
        
        return 0
    
    print("💡 Système d'apprentissage ARC AGI initialisé")
    print("Utilisez --test pour tester ou --validate pour valider")


if __name__ == "__main__":
    import sys
    sys.exit(main())