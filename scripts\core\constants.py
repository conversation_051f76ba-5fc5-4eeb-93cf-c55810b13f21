"""
Constantes et codes de retour pour le système d'analyse automatisée ARC AGI.

Ce module définit toutes les constantes utilisées dans le système,
incluant les codes de sortie, configurations par défaut, et limites.
"""

from enum import IntEnum
from typing import Dict, Any


class ExitCodes(IntEnum):
    """Codes de sortie pour l'interface CLI."""
    SUCCESS = 0
    PUZZLE_NOT_FOUND = 1
    ANALYSIS_GENERATION_FAILED = 2
    CHAT_SESSION_ERROR = 3
    PARSING_ERROR = 4
    VALIDATION_ERROR = 5
    LEARNING_ERROR = 6
    FILE_ORGANIZATION_ERROR = 7
    CONFIGURATION_ERROR = 8
    UNKNOWN_ERROR = 99


class AnalysisStatus:
    """Statuts possibles pour une analyse."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED_SUCCESS = "completed_success"
    COMPLETED_FAILURE = "completed_failure"
    ERROR = "error"


class PuzzleSubsets:
    """Sous-ensembles de puzzles disponibles."""
    TRAINING = "training"
    EVALUATION = "evaluation"
    ALL = [TRAINING, EVALUATION]


class ChatSessionTypes:
    """Types de sessions chat disponibles."""
    DEFAULT = "default"
    BLIND_ANALYSIS = "blind_analysis"
    LEARNING_EXTRACTION = "learning_extraction"
    VALIDATION_REVIEW = "validation_review"


class AIProviders:
    """Fournisseurs d'IA disponibles."""
    SIMULATION = "simulation"
    OLLAMA = "ollama"
    OPENAI = "openai"
    OPENROUTER = "openrouter"
    GROQ = "groq"
    HUGGINGFACE = "huggingface"
    CLAUDE = "claude"


class FileExtensions:
    """Extensions de fichiers utilisées."""
    JSON = ".json"
    TXT = ".txt"
    MD = ".md"
    PY = ".py"


class DirectoryPaths:
    """Chemins des répertoires du système."""
    ANALYSIS_DATA = "analysis_data"
    ARC_RESULTS = "arc_results"
    SCRIPTS_CORE = "scripts/core"
    SCRIPTS_UTILS = "scripts/utils"
    SCRIPTS_TESTS = "scripts/tests"
    DOCS = "docs"
    TRAINING_DATA = "analysis_data/training"
    EVALUATION_DATA = "analysis_data/evaluation"


class FilePatterns:
    """Patterns de noms de fichiers."""
    PUZZLE_FILE = "{puzzle_id}.json"
    SUCCESS_RESULT = "puzzle_{puzzle_id}_success.txt"
    FAILURE_RESULT = "puzzle_{puzzle_id}_failure.txt"
    ANALYSIS_CACHE = "analysis_{puzzle_id}.json"


class ValidationThresholds:
    """Seuils pour la validation des solutions."""
    PERFECT_ACCURACY = 100.0
    HIGH_ACCURACY = 95.0
    MEDIUM_ACCURACY = 80.0
    LOW_ACCURACY = 50.0


class SystemLimits:
    """Limites du système."""
    MAX_GRID_SIZE = 30  # Taille maximale d'une grille ARC
    MIN_GRID_SIZE = 1   # Taille minimale d'une grille ARC
    MAX_RESPONSE_LENGTH = 50000  # Longueur maximale d'une réponse IA
    MAX_REASONING_STEPS = 20     # Nombre maximum d'étapes de raisonnement
    MAX_RETRY_ATTEMPTS = 3       # Tentatives maximales pour les opérations
    CHAT_TIMEOUT_SECONDS = 120   # Timeout pour les sessions chat
    MAX_INSIGHTS_PER_PUZZLE = 10 # Insights maximum par puzzle


class DefaultConfigurations:
    """Configurations par défaut du système."""
    CHAT_SESSION_CONFIG: Dict[str, Any] = {
        "timeout": SystemLimits.CHAT_TIMEOUT_SECONDS,
        "max_retries": SystemLimits.MAX_RETRY_ATTEMPTS,
        "session_type": ChatSessionTypes.DEFAULT,
        "ai_provider": AIProviders.SIMULATION
    }
    
    OLLAMA_CONFIG: Dict[str, Any] = {
        "base_url": "http://localhost:11434",
        "model": "qwen3-arc:latest",  # Modèle par défaut
        "temperature": 0.1,
        "max_tokens": 4000,
        "timeout": 600  # 10 minutes pour les gros modèles
    }
    
    OPENAI_CONFIG: Dict[str, Any] = {
        "model": "gpt-4o-mini",
        "temperature": 0.1,
        "max_tokens": 4000,
        "timeout": 60
    }

    GROQ_CONFIG: Dict[str, Any] = {
        "base_url": "https://api.groq.com/openai/v1",
        "model": "moonshotai/kimi-k2-instruct",
        "temperature": 0.1,
        # max_tokens sera lu depuis .env
        "timeout": 300  # 5 minutes pour les gros modèles gratuits
    }

    OPENROUTER_CONFIG: Dict[str, Any] = {
        "base_url": "https://openrouter.ai/api/v1",
        "model": "qwen/qwen3-30b-a3b:free",
        "temperature": 0.1,
        # max_tokens sera lu depuis .env
        "timeout": 300  # 5 minutes pour les gros modèles gratuits
    }
    
    HUGGINGFACE_CONFIG: Dict[str, Any] = {
        "model": "microsoft/DialoGPT-large",
        "temperature": 0.1,
        "max_tokens": 4000,
        "timeout": 60
    }
    
    VALIDATION_CONFIG: Dict[str, Any] = {
        "generate_diagnostic": True,
        "calculate_statistics": True,
        "save_results": True
    }
    
    LEARNING_CONFIG: Dict[str, Any] = {
        "enable_extraction": True,
        "validate_genericity": True,
        "prevent_contamination": True,
        "max_insights": SystemLimits.MAX_INSIGHTS_PER_PUZZLE
    }
    
    OUTPUT_FORMAT_CONFIG: Dict[str, Any] = {
        "include_metadata": True,
        "include_reasoning": True,
        "include_validation": True,
        "structured_format": True
    }


class ColorCodes:
    """Codes de couleurs ARC AGI standard."""
    BLACK = 0
    BLUE = 1
    RED = 2
    GREEN = 3
    YELLOW = 4
    GREY = 5
    MAGENTA = 6
    ORANGE = 7
    SKY = 8
    BROWN = 9
    
    ALL_COLORS = [BLACK, BLUE, RED, GREEN, YELLOW, GREY, MAGENTA, ORANGE, SKY, BROWN]
    COLOR_NAMES = {
        BLACK: "noir",
        BLUE: "bleu",
        RED: "rouge",
        GREEN: "vert",
        YELLOW: "jaune",
        GREY: "gris",
        MAGENTA: "magenta",
        ORANGE: "orange",
        SKY: "ciel",
        BROWN: "marron"
    }


class LoggingLevels:
    """Niveaux de logging pour le système."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class MessageTemplates:
    """Templates de messages pour l'interface utilisateur."""
    ANALYSIS_START = "🔍 Début de l'analyse du puzzle {puzzle_id}..."
    ANALYSIS_SUCCESS = "✅ Puzzle {puzzle_id} résolu avec succès!"
    ANALYSIS_FAILURE = "❌ Échec de résolution du puzzle {puzzle_id}"
    LOADING_PUZZLE = "📂 Chargement du puzzle {puzzle_id}..."
    GENERATING_PROMPT = "🤖 Génération du prompt amélioré..."
    CHAT_SESSION = "💬 Envoi à la session chat..."
    PARSING_RESPONSE = "🔍 Analyse de la réponse IA..."
    VALIDATING_SOLUTION = "✅ Validation de la solution..."
    EXTRACTING_INSIGHTS = "🧠 Extraction d'insights pour l'apprentissage..."
    SAVING_RESULTS = "💾 Sauvegarde des résultats..."


class RegexPatterns:
    """Patterns regex pour l'extraction de données."""
    GRID_PATTERN = r'\[\[.*?\]\]'
    COLOR_TRANSFORMATION = r'(\d+)→(\d+)'
    REASONING_STEP = r'^\d+\.\s+'
    TRANSFORMATION_TYPE = r'(color_\w+|spatial_\w+|pattern_\w+)'


# Validation des constantes au chargement du module
def _validate_constants():
    """Valide la cohérence des constantes définies."""
    # Vérifier que tous les codes de couleur sont dans la plage valide
    for color in ColorCodes.ALL_COLORS:
        if not 0 <= color <= 9:
            raise ValueError(f"Code couleur invalide: {color}")
    
    # Vérifier que tous les noms de couleurs sont définis
    for color in ColorCodes.ALL_COLORS:
        if color not in ColorCodes.COLOR_NAMES:
            raise ValueError(f"Nom manquant pour la couleur {color}")
    
    # Vérifier la cohérence des limites
    if SystemLimits.MIN_GRID_SIZE >= SystemLimits.MAX_GRID_SIZE:
        raise ValueError("MIN_GRID_SIZE doit être < MAX_GRID_SIZE")


# Exécuter la validation au chargement
_validate_constants()