"""
Tests pour le système de session chat.

Ce module teste toutes les fonctionnalités de la classe ChatSession,
incluant la création de sessions, l'envoi de prompts, la gestion
d'erreurs et les différents types de sessions.
"""

import pytest
import time
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from scripts.core.chat_session import (
    ChatSession, 
    ChatMessage, 
    SessionConfig, 
    ChatSessionError,
    create_chat_session
)
from scripts.core.constants import ChatSessionTypes, SystemLimits


class TestChatMessage:
    """Tests pour la classe ChatMessage."""
    
    def test_chat_message_creation(self):
        """Test la création d'un message chat."""
        message = ChatMessage(role="user", content="Test message")
        
        assert message.role == "user"
        assert message.content == "Test message"
        assert isinstance(message.timestamp, datetime)
    
    def test_chat_message_to_dict(self):
        """Test la conversion d'un message en dictionnaire."""
        message = ChatMessage(role="assistant", content="Response")
        result = message.to_dict()
        
        assert result["role"] == "assistant"
        assert result["content"] == "Response"
        assert "timestamp" in result
        assert isinstance(result["timestamp"], str)


class TestSessionConfig:
    """Tests pour la classe SessionConfig."""
    
    def test_default_config(self):
        """Test la configuration par défaut."""
        config = SessionConfig()
        
        assert config.session_type == ChatSessionTypes.DEFAULT
        assert config.timeout == SystemLimits.CHAT_TIMEOUT_SECONDS
        assert config.max_retries == SystemLimits.MAX_RETRY_ATTEMPTS
        assert config.enable_history is True
        assert config.max_history_length == 50
    
    def test_custom_config(self):
        """Test une configuration personnalisée."""
        config = SessionConfig(
            session_type=ChatSessionTypes.BLIND_ANALYSIS,
            timeout=60,
            max_retries=5,
            enable_history=False,
            max_history_length=20
        )
        
        assert config.session_type == ChatSessionTypes.BLIND_ANALYSIS
        assert config.timeout == 60
        assert config.max_retries == 5
        assert config.enable_history is False
        assert config.max_history_length == 20


class TestChatSession:
    """Tests pour la classe ChatSession."""
    
    def test_session_initialization(self):
        """Test l'initialisation d'une session."""
        session = ChatSession()
        
        assert session.config.session_type == ChatSessionTypes.DEFAULT
        assert isinstance(session.session_id, str)
        assert session.session_id.startswith("chat_session_")
        assert len(session.conversation_history) == 0
        assert session.is_active is False
        assert isinstance(session.created_at, datetime)
    
    def test_session_with_custom_config(self):
        """Test l'initialisation avec une configuration personnalisée."""
        config = SessionConfig(session_type=ChatSessionTypes.BLIND_ANALYSIS)
        session = ChatSession(config)
        
        assert session.config.session_type == ChatSessionTypes.BLIND_ANALYSIS
    
    def test_invalid_session_type(self):
        """Test la validation d'un type de session invalide."""
        config = SessionConfig(session_type="invalid_type")
        
        with pytest.raises(ChatSessionError, match="Type de session invalide"):
            ChatSession(config)
    
    def test_invalid_timeout(self):
        """Test la validation d'un timeout invalide."""
        config = SessionConfig(timeout=0)
        
        with pytest.raises(ChatSessionError, match="Le timeout doit être positif"):
            ChatSession(config)
    
    def test_invalid_max_retries(self):
        """Test la validation d'un max_retries invalide."""
        config = SessionConfig(max_retries=-1)
        
        with pytest.raises(ChatSessionError, match="max_retries ne peut pas être négatif"):
            ChatSession(config)
    
    def test_create_new_session(self):
        """Test la création d'une nouvelle session."""
        session = ChatSession()
        result = session.create_new_session()
        
        assert result is True
        assert session.is_active is True
        assert len(session.conversation_history) == 0
    
    def test_create_blind_analysis_session(self):
        """Test la création d'une session d'analyse à l'aveugle."""
        config = SessionConfig(session_type=ChatSessionTypes.BLIND_ANALYSIS)
        session = ChatSession(config)
        session.create_new_session()
        
        assert session.is_active is True
        assert session.config.enable_history is False
    
    def test_send_prompt_success(self):
        """Test l'envoi réussi d'un prompt."""
        session = ChatSession()
        session.create_new_session()
        
        response = session.send_prompt("Test prompt")
        
        assert isinstance(response, str)
        assert len(response) > 0
        assert len(session.conversation_history) == 2  # user + assistant
    
    def test_send_prompt_without_active_session(self):
        """Test l'envoi d'un prompt sans session active."""
        session = ChatSession()
        
        with pytest.raises(ChatSessionError, match="Session non active"):
            session.send_prompt("Test prompt")
    
    def test_send_empty_prompt(self):
        """Test l'envoi d'un prompt vide."""
        session = ChatSession()
        session.create_new_session()
        
        with pytest.raises(ChatSessionError, match="Le prompt ne peut pas être vide"):
            session.send_prompt("")
    
    def test_send_prompt_with_history_disabled(self):
        """Test l'envoi d'un prompt avec historique désactivé."""
        config = SessionConfig(enable_history=False)
        session = ChatSession(config)
        session.create_new_session()
        
        response = session.send_prompt("Test prompt")
        
        assert isinstance(response, str)
        assert len(session.conversation_history) == 0
    
    @patch('scripts.core.chat_session.time.sleep')
    def test_send_prompt_with_retry(self, mock_sleep):
        """Test l'envoi d'un prompt avec retry en cas d'erreur."""
        config = SessionConfig(max_retries=2)
        session = ChatSession(config)
        session.create_new_session()
        
        # Le prompt contient "test_error" pour déclencher une erreur simulée
        # au premier essai, mais réussir au second
        with patch.object(session, '_send_to_api') as mock_api:
            mock_api.side_effect = [Exception("Erreur simulée"), "Réponse réussie"]
            
            response = session.send_prompt("test_error prompt")
            
            assert response == "Réponse réussie"
            assert mock_api.call_count == 2
            mock_sleep.assert_called_once_with(1)  # backoff exponentiel: 2^0 = 1
    
    @patch('scripts.core.chat_session.time.sleep')
    def test_send_prompt_max_retries_exceeded(self, mock_sleep):
        """Test l'échec après dépassement du nombre maximum de tentatives."""
        config = SessionConfig(max_retries=1)
        session = ChatSession(config)
        session.create_new_session()
        
        with patch.object(session, '_send_to_api') as mock_api:
            mock_api.side_effect = Exception("Erreur persistante")
            
            with pytest.raises(ChatSessionError, match="Échec d'envoi après 2 tentatives"):
                session.send_prompt("Test prompt")
            
            assert mock_api.call_count == 2
    
    def test_different_session_types_responses(self):
        """Test les différents types de réponses selon le type de session."""
        # Test session d'analyse à l'aveugle
        blind_session = ChatSession(SessionConfig(session_type=ChatSessionTypes.BLIND_ANALYSIS))
        blind_session.create_new_session()
        blind_response = blind_session.send_prompt("Analyse ce puzzle")
        assert "TRANSFORMATIONS DÉTECTÉES" in blind_response
        
        # Test session d'extraction d'apprentissage
        learning_session = ChatSession(SessionConfig(session_type=ChatSessionTypes.LEARNING_EXTRACTION))
        learning_session.create_new_session()
        learning_response = learning_session.send_prompt("Extraire insights")
        assert "Insights extraits" in learning_response
        
        # Test session de validation
        validation_session = ChatSession(SessionConfig(session_type=ChatSessionTypes.VALIDATION_REVIEW))
        validation_session.create_new_session()
        validation_response = validation_session.send_prompt("Valider solution")
        assert "Validation de la solution" in validation_response
    
    def test_conversation_history(self):
        """Test la gestion de l'historique de conversation."""
        session = ChatSession()
        session.create_new_session()
        
        session.send_prompt("Premier message")
        session.send_prompt("Deuxième message")
        
        history = session.get_conversation_history()
        
        assert len(history) == 4  # 2 user + 2 assistant
        assert history[0]["role"] == "user"
        assert history[0]["content"] == "Premier message"
        assert history[1]["role"] == "assistant"
        assert history[2]["role"] == "user"
        assert history[2]["content"] == "Deuxième message"
    
    def test_history_length_limit(self):
        """Test la limitation de la longueur de l'historique."""
        config = SessionConfig(max_history_length=4)
        session = ChatSession(config)
        session.create_new_session()
        
        # Envoyer plus de messages que la limite
        for i in range(3):
            session.send_prompt(f"Message {i}")
        
        history = session.get_conversation_history()
        assert len(history) == 4  # Limité à max_history_length
    
    def test_clear_history(self):
        """Test l'effacement de l'historique."""
        session = ChatSession()
        session.create_new_session()
        
        session.send_prompt("Test message")
        assert len(session.conversation_history) > 0
        
        session.clear_history()
        assert len(session.conversation_history) == 0
    
    def test_get_session_info(self):
        """Test la récupération des informations de session."""
        session = ChatSession()
        session.create_new_session()
        
        info = session.get_session_info()
        
        assert "session_id" in info
        assert info["session_type"] == ChatSessionTypes.DEFAULT
        assert info["is_active"] is True
        assert "created_at" in info
        assert "last_activity" in info
        assert info["message_count"] == 0
        assert "config" in info
    
    def test_close_session(self):
        """Test la fermeture d'une session."""
        session = ChatSession()
        session.create_new_session()
        session.send_prompt("Test message")
        
        session.close_session()
        
        assert session.is_active is False
    
    def test_close_session_clears_history_when_disabled(self):
        """Test que la fermeture efface l'historique quand il est désactivé."""
        config = SessionConfig(enable_history=False)
        session = ChatSession(config)
        session.create_new_session()
        
        # Ajouter manuellement un message pour tester
        session.conversation_history.append(ChatMessage("user", "test"))
        
        session.close_session()
        
        assert len(session.conversation_history) == 0
    
    def test_context_manager(self):
        """Test l'utilisation comme context manager."""
        session = ChatSession()
        
        with session as s:
            assert s.is_active is True
            response = s.send_prompt("Test dans context manager")
            assert isinstance(response, str)
        
        assert session.is_active is False
    
    def test_context_manager_with_exception(self):
        """Test le context manager avec une exception."""
        session = ChatSession()
        
        try:
            with session as s:
                s.send_prompt("Test message")
                raise ValueError("Test exception")
        except ValueError:
            pass
        
        assert session.is_active is False


class TestChatSessionFactory:
    """Tests pour la fonction factory create_chat_session."""
    
    def test_create_default_session(self):
        """Test la création d'une session par défaut."""
        session = create_chat_session()
        
        assert isinstance(session, ChatSession)
        assert session.config.session_type == ChatSessionTypes.DEFAULT
        assert session.is_active is True
    
    def test_create_blind_analysis_session(self):
        """Test la création d'une session d'analyse à l'aveugle."""
        session = create_chat_session(ChatSessionTypes.BLIND_ANALYSIS)
        
        assert session.config.session_type == ChatSessionTypes.BLIND_ANALYSIS
        assert session.is_active is True
    
    def test_create_learning_session(self):
        """Test la création d'une session d'apprentissage."""
        session = create_chat_session(ChatSessionTypes.LEARNING_EXTRACTION)
        
        assert session.config.session_type == ChatSessionTypes.LEARNING_EXTRACTION
        assert session.is_active is True
    
    def test_create_validation_session(self):
        """Test la création d'une session de validation."""
        session = create_chat_session(ChatSessionTypes.VALIDATION_REVIEW)
        
        assert session.config.session_type == ChatSessionTypes.VALIDATION_REVIEW
        assert session.is_active is True


class TestChatSessionIntegration:
    """Tests d'intégration pour le système de session chat."""
    
    def test_complete_conversation_flow(self):
        """Test un flux complet de conversation."""
        session = ChatSession()
        session.create_new_session()
        
        # Simuler une conversation complète
        response1 = session.send_prompt("Analyse ce puzzle ARC")
        assert len(response1) > 0
        
        response2 = session.send_prompt("Peux-tu détailler les transformations?")
        assert len(response2) > 0
        
        # Vérifier l'historique
        history = session.get_conversation_history()
        assert len(history) == 4
        
        # Vérifier les informations de session
        info = session.get_session_info()
        assert info["message_count"] == 4
        
        session.close_session()
    
    def test_multiple_sessions_isolation(self):
        """Test l'isolation entre plusieurs sessions."""
        # Ajouter un petit délai pour garantir des timestamps différents
        session1 = create_chat_session(ChatSessionTypes.DEFAULT)
        time.sleep(0.001)  # Délai minimal pour différencier les timestamps
        session2 = create_chat_session(ChatSessionTypes.BLIND_ANALYSIS)
        
        session1.send_prompt("Message session 1")
        session2.send_prompt("Message session 2")
        
        # Vérifier que les sessions sont isolées
        assert session1.session_id != session2.session_id
        assert len(session1.conversation_history) == 2
        assert len(session2.conversation_history) == 0  # Histoire désactivée pour blind_analysis
        
        session1.close_session()
        session2.close_session()
    
    def test_session_timeout_simulation(self):
        """Test la simulation d'un timeout de session."""
        config = SessionConfig(timeout=1)  # Timeout très court
        session = ChatSession(config)
        session.create_new_session()
        
        # Dans une vraie implémentation, ceci testerait le timeout
        # Pour l'instant, on teste juste que la configuration est respectée
        assert session.config.timeout == 1
    
    @patch('scripts.core.chat_session.time.sleep')
    def test_error_recovery_and_logging(self, mock_sleep):
        """Test la récupération d'erreurs et le logging."""
        session = ChatSession()
        session.create_new_session()
        
        with patch.object(session, '_send_to_api') as mock_api:
            # Première tentative échoue, deuxième réussit
            mock_api.side_effect = [
                Exception("Erreur réseau temporaire"),
                "Réponse après récupération"
            ]
            
            response = session.send_prompt("Test recovery")
            
            assert response == "Réponse après récupération"
            assert mock_api.call_count == 2


if __name__ == "__main__":
    # Exécuter les tests
    pytest.main([__file__, "-v"])