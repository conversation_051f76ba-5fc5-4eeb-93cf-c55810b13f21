#!/usr/bin/env python3
"""
Générateur de prompts pour la résolution ARC AGI
Utilise des lettres simples pour représenter les couleurs (compatible avec tous les systèmes)
"""

import json
import os
import sys
import argparse
from pathlib import Path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'utils'))
from extract_key_insights import extract_key_insights, format_insights_for_prompt

def visualize_grid(grid, background=0):
    """Convertit une grille en représentation textuelle simple avec lettres pour les couleurs"""
    # Mapping des couleurs ARC AGI vers des lettres
    color_letters = {
        0: '.',  # Fond
        1: 'B',  # Bleu
        2: 'R',  # Rouge
        3: 'G',  # Vert
        4: 'Y',  # Jaune
        5: 'S',  # Gris
        6: 'M',  # Magenta/Fuchsia
        7: 'O',  # Orange
        8: 'C',  # <PERSON>an/Teal
        9: 'N'   # Marron
    }
    
    visual = []
    for row in grid:
        visual_row = []
        for cell in row:
            visual_row.append(color_letters.get(cell, str(cell)))
        visual.append(''.join(visual_row))
    
    return '\n'.join(visual)  # IMPORTANT: Retourne avec des sauts de ligne

def extract_key_info(analysis_data):
    """Extrait les informations critiques de l'analyse"""
    try:
        # Extraire les informations de base
        puzzle_id = analysis_data.get("puzzle_id", "unknown")
        
        # Extraire la couleur de fond
        background = analysis_data["raw_analysis"]["patterns"]["motif"]["background_colors"][0]
        
        # Extraire les couleurs du motif
        motif_colors = analysis_data["raw_analysis"]["patterns"]["motif"]["motif_colors"]
        motif_type = analysis_data["raw_analysis"]["patterns"]["motif"]["type"]
        
        # Déterminer le mécanisme
        mechanism = "non défini"
        if "tags" in analysis_data and "gravity" in analysis_data["tags"]:
            mechanism = "gravity"
        elif "tags" in analysis_data and "sliding" in analysis_data["tags"]:
            mechanism = "sliding"
        
        # Déterminer la direction du mouvement
        direction = [0, 0]  # [dx, dy]
        if "common_color_changes" in analysis_data["raw_analysis"]["diff_analysis"]:
            changes = analysis_data["raw_analysis"]["diff_analysis"]["common_color_changes"]
            total_changes = sum(changes.values())
            
            # Analyser les changements pour déterminer la direction
            right_changes = 0
            left_changes = 0
            
            for change, count in changes.items():
                if "→" in change:
                    from_color, to_color = change.split("→")
                    if from_color == "0" and int(to_color) in motif_colors:
                        right_changes += count
                    elif to_color == "0" and int(from_color) in motif_colors:
                        left_changes += count
            
            # Déterminer si le mouvement est horizontal ou vertical
            if right_changes > total_changes * 0.6:  # Si plus de 60% des changements sont vers la droite
                direction = [1, 0]
            elif left_changes > total_changes * 0.6:
                direction = [-1, 0]
            else:
                # Analyser les positions pour déterminer le mouvement vertical
                if "train_diffs" in analysis_data["raw_analysis"]["diff_analysis"] and len(analysis_data["raw_analysis"]["diff_analysis"]["train_diffs"]) > 0:
                    if "modified_positions" in analysis_data["raw_analysis"]["diff_analysis"]["train_diffs"][0]:
                        positions = analysis_data["raw_analysis"]["diff_analysis"]["train_diffs"][0]["modified_positions"]
                        if positions and len(positions) > 0:
                            # Comparer les positions d'origine et de destination
                            y_values = [pos[0] for pos in positions]
                            if max(y_values) - min(y_values) > len(positions) * 0.5:
                                direction = [0, 1]  # Mouvement vertical vers le bas
        
        # Déterminer le comportement aux bords
        edge_behavior = "stops_at_obstacle"
        if "tags" in analysis_data and "wraps_around" in analysis_data["tags"]:
            edge_behavior = "wraps_around"
        
        # Déterminer les propriétés
        color_conservation = True
        structure_conservation = True
        
        # Extraire les tags pertinents
        tags = []
        if mechanism != "in_place":
            tags.append(mechanism)
        if direction[0] != 0:
            tags.append("déplacement_horizontal")
        if direction[1] != 0:
            tags.append("déplacement_vertical")
        
        return {
            "puzzle_id": puzzle_id,
            "background": background,
            "motif": {
                "colors": motif_colors,
                "type": motif_type
            },
            "transformation": {
                "type": "movement",
                "direction": direction,
                "mechanism": mechanism,
                "edge_behavior": edge_behavior
            },
            "properties": {
                "color_conservation": color_conservation,
                "structure_conservation": structure_conservation
            },
            "tags": tags
        }
    except Exception as e:
        # En cas d'erreur, retourner une analyse minimale mais fonctionnelle
        return {
            "puzzle_id": "unknown",
            "background": 0,
            "motif": {
                "colors": [1, 2, 3, 4, 5, 6, 7, 8, 9],
                "type": "unknown"
            },
            "transformation": {
                "type": "unknown",
                "direction": [0, 0],
                "mechanism": "unknown",
                "edge_behavior": "unknown"
            },
            "properties": {
                "color_conservation": True,
                "structure_conservation": True
            },
            "tags": []
        }

def generate_arc_prompt(puzzle_data, analysis_data, puzzle_id):
    """Génère un prompt optimisé pour la résolution ARC AGI avec alignement parfait"""
    # Extraire les informations critiques
    key_info = extract_key_info(analysis_data)
    
    # Si l'ID du puzzle n'est pas dans l'analyse, utiliser celui fourni
    if key_info["puzzle_id"] == "unknown" and puzzle_id != "unknown":
        key_info["puzzle_id"] = puzzle_id
    
    # Construire le prompt
    prompt = "Tu es un expert en analyse ARC AGI analyse ce puzzle en utilisant ce fichier.\n"
    prompt += "Vois le comme si c'était des cubes de couleurs sur une table et que tu regardais d'en haut.\n"
    prompt += "Tu peux déplacer, les cubes, en rajouter, en enlever, les considérer comme formant un  motif,\n"
    prompt += "les tourner, leur faire une action miroir, les changer de couleurs, etc.\n"
    prompt += "\nCorrespondances des couleurs pour faciliter le dialogue:\n"
    prompt += "0: '.' (noir ou fond),\n"
    prompt += "1: 'B' (bleu),\n"
    prompt += "2: 'R' (rouge),\n"
    prompt += "3: 'G' (vert),\n"
    prompt += "4: 'Y' (jaune),\n"
    prompt += "5: 'S' (gris),\n"
    prompt += "6: 'M' (magenta),\n"
    prompt += "7: 'O' (orange),\n"
    prompt += "8: 'C' (cyan),\n"
    prompt += "9: 'N' (marron)\n"
    prompt += f"\nPUZZLE ID: {key_info['puzzle_id']}\n"
    prompt += f"BACKGROUND: {key_info['background']} = . (point)\n\n"
    
    # Ajouter les exemples d'entraînement
    for i, example in enumerate(puzzle_data["train"]):
        prompt += f"EXAMPLE {i+1}:\n"
        prompt += f"INPUT\n"
        prompt += visualize_grid(example["input"], key_info["background"])
        prompt += "\n\n"
        
        prompt += f"OUTPUT\n"
        prompt += visualize_grid(example["output"], key_info["background"])
        prompt += "\n\n"
    
    # Ajouter les observations clés
    prompt += "KEY OBSERVATIONS:\n"
    
    # Observation 1: Type de motif
    color_names = []
    for c in key_info["motif"]["colors"]:
        color_desc = {
            1: "B (bleu)",
            2: "R (rouge)",
            3: "G (vert)",
            4: "Y (jaune)",
            5: "S (gris)",
            6: "M (magenta)",
            7: "O (orange)",
            8: "C (cyan)",
            9: "N (marron)"
        }.get(c, f"{c} (inconnu)")
        color_names.append(color_desc)
    
    prompt += f"- Motif de type '{key_info['motif']['type']}' avec les couleurs: {', '.join(color_names)}\n"
    
    # Observation 2: Transformation
    dx, dy = key_info["transformation"]["direction"]
    direction_desc = []
    if dx > 0:
        direction_desc.append(f"{dx} position(s) vers la droite")
    elif dx < 0:
        direction_desc.append(f"{abs(dx)} position(s) vers la gauche")
    if dy > 0:
        direction_desc.append(f"{dy} position(s) vers le bas")
    elif dy < 0:
        direction_desc.append(f"{abs(dy)} position(s) vers le haut")
    
    direction_str = " et ".join(direction_desc) if direction_desc else "aucun déplacement"
    prompt += f"- Transformation: déplacement {direction_str} sous l'effet de {key_info['transformation']['mechanism']}\n"
    
    # Observation 3: Propriétés
    prompt += "- Propriétés conservées: "
    properties = []
    if key_info["properties"]["color_conservation"]:
        properties.append("couleurs")
    if key_info["properties"]["structure_conservation"]:
        properties.append("structure")
    prompt += ", ".join(properties) if properties else "aucune"
    prompt += "\n"
    
    # Observation 4: Comportement aux bords
    edge_behavior = {
        "stops_at_obstacle": "s'arrête à l'obstacle",
        "wraps_around": "revient de l'autre côté"
    }.get(key_info["transformation"]["edge_behavior"], "inconnu")
    prompt += f"- Comportement aux bords: {edge_behavior}\n\n"
    
    # Ajouter les insights critiques de l'analyse automatique
    insights = extract_key_insights(analysis_data)
    insights_section = format_insights_for_prompt(insights)
    prompt += insights_section
    
    # Ajouter la règle de transformation avec un saut de ligne clair
    prompt += "RULE: "
    
    # Générer une description de règle naturelle
    if key_info["transformation"]["mechanism"] == "gravity":
        prompt += f"Les motifs se déplacent vers le bas sous l'effet de la gravité jusqu'à rencontrer un obstacle ou le bord inférieur de la grille."
    elif key_info["transformation"]["mechanism"] == "sliding":
        prompt += f"Les motifs glissent horizontalement jusqu'à atteindre un obstacle ou le bord de la grille."
    elif direction_desc:
        prompt += f"Le motif se déplace de {direction_str}."
    else:
        prompt += f"Transformation in-place détectée, sans déplacement global."
    
    prompt += "\n\n"
    prompt += "📋 Méthodologie Correcte pour Analyser un Puzzle ARC AGI\n1. Observer attentivement chaque paire input/output\n2. Formuler une hypothèse sur la transformation\n3. Tester systématiquement cette hypothèse sur TOUS les exemples d'entraînement\n4. Affiner ou rejeter l'hypothèse si elle ne fonctionne pas sur un exemple\n5. Valider que la règle fonctionne sur tous les exemples avant de l'appliquer au test\n"
    prompt += "\nSi tu as réussi, tu peux proposer une grille TEST\\OUTPUT\n"

    # Ajouter le test input avec une grille bien formatée
    prompt += "\nINPUT TEST:\n"
    prompt += visualize_grid(puzzle_data["test"][0]["input"], key_info["background"])
    prompt += "\n\n"
    
    # Ajouter l'instruction finale
    prompt += "WRITE YOUR RULE AND APPLY TO THE TEST INPUT:\n"
    
    return prompt, key_info

def save_analysis_report(key_info, output_file):
    """Sauvegarde un rapport d'analyse structuré"""
    # Créer un chemin pour le fichier d'analyse
    analysis_dir = os.path.dirname(output_file)
    analysis_file = os.path.join(analysis_dir, f"{os.path.splitext(os.path.basename(output_file))[0]}_analysis.json")
    
    with open(analysis_file, 'w') as f:
        json.dump(key_info, f, indent=2)
    
    return analysis_file

def main():
    # Configuration des chemins par défaut
    default_arc_data_dir = "../arc-puzzle/arcdata/"
    default_analysis_dir = "analysis_data"
    default_output_dir = "arc_results"
    
    # Configuration des arguments de ligne de commande
    parser = argparse.ArgumentParser(description='Générateur de prompts pour ARC AGI')
    parser.add_argument('--taskid', type=str, required=True, help='ID du puzzle à traiter')
    parser.add_argument('--subset', type=str, default='training', choices=['training', 'evaluation'], 
                        help='Sous-ensemble à utiliser (défaut: training)')
    parser.add_argument('--arc-data-dir', type=str, default=default_arc_data_dir,
                        help=f'Répertoire des données ARC (défaut: {default_arc_data_dir})')
    parser.add_argument('--analysis-dir', type=str, default=default_analysis_dir,
                        help=f'Répertoire des analyses (défaut: {default_analysis_dir})')
    parser.add_argument('--output-dir', type=str, default=default_output_dir,
                        help=f'Répertoire de sortie (défaut: {default_output_dir})')
    parser.add_argument('--quiet', action='store_true', help='Mode silencieux (pas de résumé)')
    
    args = parser.parse_args()
    
    # Construction des chemins
    puzzle_file = os.path.join(args.arc_data_dir, args.subset, f"{args.taskid}.json")
    analysis_file = os.path.join(args.analysis_dir, args.subset, f"{args.taskid}_analysis.json")
    output_file = os.path.join(args.output_dir, args.subset, f"{args.taskid}_prompt.txt")
    
    # Vérifier que les fichiers existent
    if not os.path.exists(puzzle_file):
        print(f"Erreur: Fichier puzzle non trouvé: {puzzle_file}")
        print(f"Vérifiez que le fichier {args.taskid}.json existe dans {args.arc_data_dir}/{args.subset}/")
        return
    
    if not os.path.exists(analysis_file):
        print(f"Attention: Fichier d'analyse non trouvé: {analysis_file}")
        print(f"Le script continuera avec une analyse minimale.")
        analysis_data = {"puzzle_id": args.taskid}
    else:
        # Charger les données
        try:
            with open(puzzle_file, 'r') as f:
                puzzle_data = json.load(f)
            
            with open(analysis_file, 'r') as f:
                analysis_data = json.load(f)
        except Exception as e:
            print(f"Erreur lors du chargement des fichiers: {e}")
            return
    
    # Créer le répertoire de sortie si nécessaire
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # Générer le prompt
    prompt, key_info = generate_arc_prompt(puzzle_data, analysis_data, args.taskid)
    
    # Sauvegarder le prompt
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(prompt)
        print(f"Prompt généré et sauvegardé dans {output_file}")
        
        # Sauvegarder aussi l'analyse structurée
        analysis_file = save_analysis_report(key_info, output_file)
        print(f"Analyse structurée sauvegardée dans {analysis_file}")
    except Exception as e:
        print(f"Erreur lors de la sauvegarde: {e}")
        return
    
    # Afficher un résumé des informations clés si pas en mode silencieux
    if not args.quiet:
        print("\n" + "="*50)
        print("RÉSUMÉ DES INFORMATIONS CLÉS:")
        print(f"- Puzzle ID: {key_info['puzzle_id']}")
        print(f"- Fond: {key_info['background']} = .")
        print(f"- Couleurs du motif: {key_info['motif']['colors']}")
        print(f"- Type de motif: {key_info['motif']['type']}")
        print(f"- Mécanisme: {key_info['transformation']['mechanism']}")
        print(f"- Direction: {key_info['transformation']['direction']}")
        print(f"- Tags: {', '.join(key_info['tags'])}")
        print("="*50)

if __name__ == "__main__":
    main()