import json
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from sentence_transformers import SentenceTransformer

class ARCRetrievalSystem:
    def __init__(self, puzzles_dir="arc_data/training", embeddings_model="all-MiniLM-L6-v2"):
        """Initialize the ARC retrieval system with puzzle database"""
        self.model = SentenceTransformer(embeddings_model)
        self.puzzles = []
        self.embeddings = []
        
        # Load and process all puzzles
        self._load_puzzles(puzzles_dir)
        self._create_embeddings()
    
    def _load_puzzles(self, puzzles_dir):
        """Load all ARC puzzles from directory"""
        import os
        for filename in os.listdir(puzzles_dir):
            if filename.endswith('.json'):
                with open(os.path.join(puzzles_dir, filename), 'r') as f:
                    puzzle = json.load(f)
                    puzzle_id = os.path.splitext(filename)[0]
                    
                    # Convert puzzle to text description
                    puzzle_text = self._puzzle_to_text(puzzle)
                    
                    self.puzzles.append({
                        "id": puzzle_id,
                        "text": puzzle_text,
                        "puzzle": puzzle
                    })
    
    def _puzzle_to_text(self, puzzle):
        """Convert puzzle to text representation for embedding"""
        text = "Input-output transformation examples:\n"
        
        for i, example in enumerate(puzzle['train']):
            text += f"Example {i+1}:\n"
            text += self._grid_to_text(example['input'], "Input")
            text += self._grid_to_text(example['output'], "Output")
            text += "\n"
        
        # Add test input for similarity
        text += "Test input:\n"
        text += self._grid_to_text(puzzle['test'][0]['input'], "Input")
        
        return text
    
    def _grid_to_text(self, grid, title):
        """Convert grid to text format"""
        text = f"{title} ({len(grid)}x{len(grid[0])} grid):\n"
        for row in grid:
            text += " ".join(str(x) for x in row) + "\n"
        return text
    
    def _create_embeddings(self):
        """Create embeddings for all puzzles"""
        texts = [puzzle['text'] for puzzle in self.puzzles]
        self.embeddings = self.model.encode(texts, convert_to_tensor=True)
    
    def get_similar_puzzles(self, puzzle_id=None, puzzle_data=None, k=3):
        """
        Get k most similar puzzles
        
        Args:
            puzzle_id: ID of puzzle to find similar to (from training set)
            puzzle_data: Puzzle data to find similar to (for evaluation)
            k: Number of similar puzzles to return
        """
        if puzzle_id:
            # Find puzzle by ID
            puzzle_idx = next((i for i, p in enumerate(self.puzzles) if p['id'] == puzzle_id), None)
            if puzzle_idx is None:
                return []
            query_embedding = self.embeddings[puzzle_idx]
        elif puzzle_data:
            # Create embedding for new puzzle
            puzzle_text = self._puzzle_to_text(puzzle_data)
            query_embedding = self.model.encode([puzzle_text], convert_to_tensor=True)[0]
        else:
            return []
        
        # Calculate similarities
        similarities = cosine_similarity(
            query_embedding.reshape(1, -1), 
            self.embeddings
        )[0]
        
        # Get top k similar (excluding self)
        similar_indices = np.argsort(similarities)[::-1][1:k+1]
        
        return [{
            "id": self.puzzles[idx]['id'],
            "similarity": float(similarities[idx]),
            "puzzle": self.puzzles[idx]['puzzle']
        } for idx in similar_indices]
    
    def format_rag_prompt(self, puzzle_id=None, puzzle_data=None, k=3):
        """Format RAG prompt with similar examples"""
        similar_puzzles = self.get_similar_puzzles(puzzle_id, puzzle_data, k)
        
        prompt = "You are an ARC AGI puzzle expert. Use the EXAMPLES BELOW to understand the pattern transformation rules.\n\n"
        
        # Add similar puzzles as examples
        for i, sp in enumerate(similar_puzzles):
            prompt += f"EXAMPLE {i+1} (Similar puzzle - ID: {sp['id']}, Similarity: {sp['similarity']:.2f}):\n"
            
            # Add training examples
            for j, example in enumerate(sp['puzzle']['train']):
                prompt += f"  Training pair {j+1}:\n"
                prompt += self._grid_to_text(example['input'], "    Input")
                prompt += self._grid_to_text(example['output'], "    Output")
                prompt += "\n"
            
            # Add the solution rule (this is the key!)
            rule = self._infer_rule(sp['puzzle'])
            prompt += f"  SOLUTION RULE: {rule}\n\n"
        
        prompt += "NOW SOLVE THE CURRENT PUZZLE:\n"
        return prompt
    
    def _infer_rule(self, puzzle):
        """
        Infer the transformation rule from a puzzle
        This is a simplified version - you'd use your pre-analysis files here
        """
        # In a real implementation, you'd use your pre-analysis files here
        # For demonstration, we'll return a generic rule
        return "Transform the input grid by identifying the pattern of number changes between input and output grids. The rule involves a specific spatial transformation that consistently applies to all examples."