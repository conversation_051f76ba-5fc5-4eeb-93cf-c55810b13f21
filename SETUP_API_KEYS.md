# 🔑 Configuration des Clés API

Ce guide vous explique comment configurer les clés API pour utiliser différents fournisseurs d'IA avec le système ARC AGI.

## 📋 Étapes de Configuration

### 1. <PERSON><PERSON><PERSON> le fichier de configuration

```bash
# Copiez le fichier template
cp .env.example .env
```

### 2. Obtenir vos clés API

#### 🧠 OpenAI
1. Allez sur [OpenAI Platform](https://platform.openai.com/api-keys)
2. Créez un compte ou connectez-vous
3. Cliquez sur "Create new secret key"
4. Copiez la clé générée

#### 🧠 Groq OpenAI Compatible
1. Allez sur [Groq Platform](https://api.groq.com/openai/v1)
2. Créez un compte ou connectez-vous
3. Cliquez sur "Create new secret key"
4. Copiez la clé générée

#### 🌐 OpenRouter
1. Allez sur [OpenRouter](https://openrouter.ai/keys)
2. C<PERSON>ez un compte ou connectez-vous
3. C<PERSON><PERSON> sur "Create Key"
4. Copie<PERSON> la clé générée

#### 🤗 Hugging Face
1. Allez sur [Hugging Face Tokens](https://huggingface.co/settings/tokens)
2. Créez un compte ou connectez-vous
3. Cliquez sur "New token"
4. Sélectionnez "Read" comme type
5. Copiez le token généré

### 3. Configurer le fichier .env

Ouvrez le fichier `.env` et remplacez les valeurs par vos vraies clés :

```bash
# Remplacez par vos vraies clés
OPENAI_API_KEY=sk-proj-abcd1234...
OPENROUTER_API_KEY=sk-or-v1-abcd1234...
GROQ_API_KEY=sk-groq-abcd1234...
HUGGINGFACE_API_KEY=hf_abcd1234...
```

### 4. Vérifier la configuration

```bash
# Vérifiez que vos clés sont bien configurées
python arc_enhanced_prompt.py env-status

# Testez les connexions
python arc_enhanced_prompt.py test-ai
```

## 🎯 Modèles Recommandés

### Pour l'analyse ARC AGI

#### OpenAI
- `gpt-4o-mini` : Rapide et économique
- `gpt-4o` : Plus puissant mais plus cher

#### Groq
- `moonshotai/kimi-k2-instruct` : Excellent pour le raisonnement

#### OpenRouter
- `anthropic/claude-3.5-sonnet` : Excellent pour le raisonnement
- `meta-llama/llama-3.1-8b-instruct` : Bon rapport qualité/prix
- `google/gemini-pro` : Alternative intéressante

#### Hugging Face
- `microsoft/DialoGPT-large` : Bon pour les conversations
- `facebook/blenderbot-400M-distill` : Plus léger

## 💰 Coûts Approximatifs

### OpenAI (par 1M tokens)
- GPT-4o-mini : ~$0.15 input / $0.60 output
- GPT-4o : ~$5.00 input / $15.00 output

### OpenRouter
- Varie selon le modèle (généralement moins cher qu'OpenAI direct)
- Claude-3.5-Sonnet : ~$3.00 input / $15.00 output

### Hugging Face
- Gratuit avec limitations
- Payant pour usage intensif

## 🔧 Configuration Avancée

### Modèles par défaut personnalisés

```bash
# Dans votre fichier .env
OPENAI_DEFAULT_MODEL=gpt-4o-mini
OPENROUTER_DEFAULT_MODEL=deepseek/deepseek-r1-0528:free
HUGGINGFACE_DEFAULT_MODEL=microsoft/DialoGPT-large
OLLAMA_DEFAULT_MODEL=qwen3-arc:latest
```

### URL Ollama personnalisée

```bash
# Si Ollama tourne sur un autre port/serveur
OLLAMA_BASE_URL=http://localhost:11434
```

## 🚀 Utilisation

### Analyse avec différents providers

```bash
# Avec OpenAI
python arc_enhanced_prompt.py analyze --puzzle-id d687bc17 --ai-provider openai

# Avec Groq
python arc_enhanced_prompt.py analyze --puzzle-id d687bc17 --ai-provider groq

# Avec OpenRouter
python arc_enhanced_prompt.py analyze --puzzle-id d687bc17 --ai-provider openrouter

# Avec un modèle spécifique
python arc_enhanced_prompt.py analyze --puzzle-id d687bc17 --ai-provider openrouter --openrouter-model deepseek/deepseek-r1-0528:free
```

### Test des connexions

```bash
# Tester tous les providers
python arc_enhanced_prompt.py test-ai

# Tester un provider spécifique
python arc_enhanced_prompt.py test-ai --provider openai
```

## 🛡️ Sécurité

- ⚠️ **Ne jamais committer le fichier `.env`** (il est dans `.gitignore`)
- 🔒 Gardez vos clés API secrètes
- 🔄 Régénérez vos clés si elles sont compromises
- 📊 Surveillez votre usage pour éviter les coûts inattendus

## 🆘 Dépannage

### Erreur "Clé API manquante"
- Vérifiez que le fichier `.env` existe
- Vérifiez que la clé est bien définie (pas de `your_xxx_api_key_here`)
- Redémarrez votre terminal après modification du `.env`

### Erreur "API non accessible"
- Vérifiez votre connexion internet
- Vérifiez que la clé API est valide
- Vérifiez les quotas de votre compte

### Timeout
- Certains modèles sont plus lents
- Essayez un modèle plus petit/rapide
- Vérifiez la charge du service