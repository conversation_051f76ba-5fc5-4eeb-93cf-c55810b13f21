#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script principal pour générer des prompts ARC AGI améliorés avec insights automatiques
"""

import argparse
import os
import sys
import subprocess
from pathlib import Path

# Forcer l'encodage UTF-8 pour la sortie
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
    sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())

def ensure_analysis_exists(taskid, analysis_file, quiet=False):
    """S'assure que l'analyse existe, la génère si nécessaire"""
    
    if os.path.exists(analysis_file):
        if not quiet:
            print(f"Analyse trouvee: {analysis_file}")
        return True
    
    if not quiet:
        print(f"Analyse manquante pour {taskid}")
        print(f"Generation automatique de l'analyse...")
    
    try:
        # Appeler generate_analysis.py pour créer l'analyse
        analysis_script = os.path.join(os.path.dirname(__file__), "generate_analysis.py")
        cmd = ["python", analysis_script, "--taskid", taskid]
        
        if quiet:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        else:
            result = subprocess.run(cmd, check=True)
        
        # Vérifier que l'analyse a bien été créée
        if os.path.exists(analysis_file):
            if not quiet:
                print(f"Analyse generee avec succes: {analysis_file}")
            return True
        else:
            if not quiet:
                print(f"Echec de la generation de l'analyse")
            return False
            
    except subprocess.CalledProcessError as e:
        if not quiet:
            print(f"Erreur lors de la generation de l'analyse: {e}")
        return False
    except Exception as e:
        if not quiet:
            print(f"Erreur inattendue: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(
        description='Générateur de prompts ARC AGI avec insights automatiques',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Exemples d'utilisation:
  python generate_enhanced_prompt.py --taskid 2204b7a8
  python generate_enhanced_prompt.py --taskid 2204b7a8 --subset evaluation
  python generate_enhanced_prompt.py --taskid 2204b7a8 --show-insights-only
  
Note: L'analyse sera générée automatiquement si elle n'existe pas.
        """
    )
    
    parser.add_argument('--taskid', type=str, required=True, 
                        help='ID du puzzle à traiter')
    parser.add_argument('--subset', type=str, default='training', 
                        choices=['training', 'evaluation'],
                        help='Sous-ensemble à utiliser (défaut: training)')
    parser.add_argument('--arc-data-dir', type=str, default='../../arc-puzzle/arcdata/',
                        help='Répertoire des données ARC')
    parser.add_argument('--analysis-dir', type=str, default='../../analysis_data',
                        help='Répertoire des analyses')
    parser.add_argument('--output-dir', type=str, default='../../arc_results',
                        help='Répertoire de sortie')
    parser.add_argument('--show-insights-only', action='store_true',
                        help='Afficher seulement les insights sans générer le prompt complet')
    parser.add_argument('--quiet', action='store_true',
                        help='Mode silencieux')
    parser.add_argument('--force-regenerate', action='store_true',
                        help='Forcer la régénération de l\'analyse même si elle existe')
    
    args = parser.parse_args()
    
    # Vérifier que le fichier puzzle existe
    puzzle_file = os.path.join(args.arc_data_dir, args.subset, f"{args.taskid}.json")
    if not os.path.exists(puzzle_file):
        print(f"Erreur: Fichier puzzle non trouve: {puzzle_file}")
        print(f"Conseil: Verifiez que {args.taskid} existe dans {args.arc_data_dir}/{args.subset}/")
        return 1
    
    # S'assurer que l'analyse existe (la générer si nécessaire)
    analysis_file = os.path.join(args.analysis_dir, args.subset, f"{args.taskid}_analysis.json")
    
    # Forcer la régénération si demandé
    if args.force_regenerate and os.path.exists(analysis_file):
        if not args.quiet:
            print(f"Regeneration forcee de l'analyse...")
        os.remove(analysis_file)
    
    # Générer l'analyse si nécessaire
    if not ensure_analysis_exists(args.taskid, analysis_file, args.quiet):
        print(f"Erreur: Impossible de générer ou trouver l'analyse pour {args.taskid}")
        return 1
    
    # Si on veut seulement les insights
    if args.show_insights_only:
        extract_script = os.path.join(os.path.dirname(__file__), "..", "utils", "extract_key_insights.py")
        cmd = ["python", extract_script, "--analysis-file", analysis_file]
        subprocess.run(cmd)
        return 0
    
    # Générer le prompt complet
    if not args.quiet:
        print(f"Generation du prompt ameliore pour {args.taskid}...")
    
    prompt_generator = os.path.join(os.path.dirname(__file__), "arc_prompt_generator4.py")
    cmd = [
        "python", prompt_generator,
        "--taskid", args.taskid,
        "--subset", args.subset,
        "--arc-data-dir", args.arc_data_dir,
        "--analysis-dir", args.analysis_dir,
        "--output-dir", args.output_dir
    ]
    
    if args.quiet:
        cmd.append("--quiet")
    
    try:
        result = subprocess.run(cmd, check=True)
        
        if not args.quiet:
            output_file = os.path.join(args.output_dir, args.subset, f"{args.taskid}_prompt.txt")
            print(f"Prompt ameliore genere: {output_file}")
            
            # Afficher un aperçu des insights
            print("\nApercu des insights integres:")
            extract_script = os.path.join(os.path.dirname(__file__), "..", "utils", "extract_key_insights.py")
            subprocess.run(["python", extract_script, 
                          "--analysis-file", analysis_file], 
                         capture_output=False)
        
        return 0
        
    except subprocess.CalledProcessError as e:
        print(f"Erreur lors de la generation: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())