---
inclusion: always
priority: high
---

# 📚 RÈGLES STRICTES - Politique de Documentation

## ⚠️ PRINCIPE FONDAMENTAL - PAS DE DOCUMENTATION AUTOMATIQUE

**RÈGLE ABSOLUE** : Ne **JAMAIS** créer de documentation sauf si **explicitement demandé** par l'utilisateur.

## 🚫 INTERDICTIONS STRICTES

### Création de Documentation INTERDITE
- ❌ **AUCUN fichier README** automatique (`README_*.md`, `GUIDE_*.md`)
- ❌ **AUCUN fichier SUMMARY** automatique (`SUMMARY_*.md`, `OVERVIEW_*.md`)
- ❌ **AUCUN fichier de guide** automatique (`MIGRATION_*.md`, `TUTORIAL_*.md`)
- ❌ **AUCUN fichier d'organisation** automatique (`ORGANIZATION_*.md`, `STRUCTURE_*.md`)
- ❌ **AUCUN fichier de résumé** automatique (`FINAL_*.md`, `COMPLETE_*.md`)
- ❌ **AUCUNE documentation technique** automatique sans demande explicite

### Actions INTERDITES
- ❌ **Créer** des fichiers `.md` dans `docs/` sans demande
- ❌ **Générer** des guides ou tutoriels automatiquement
- ❌ **Produire** des résumés ou rapports non demandés
- ❌ **Écrire** de la documentation "pour compléter" le projet
- ❌ **Ajouter** des explications détaillées non sollicitées

## ✅ ACTIONS AUTORISÉES UNIQUEMENT

### Documentation Autorisée SEULEMENT Si Demandée
- ✅ **Créer** de la documentation **uniquement** sur demande explicite
- ✅ **Modifier** la documentation existante si demandé
- ✅ **Supprimer** la documentation obsolète si demandé
- ✅ **Réorganiser** la documentation existante si demandé

### Phrases Qui Autorisent la Documentation
- ✅ "Peux-tu créer un README pour..."
- ✅ "J'aimerais un guide pour..."
- ✅ "Écris-moi une documentation sur..."
- ✅ "Fais-moi un résumé de..."
- ✅ "Documente cette fonctionnalité"

### Phrases Qui N'Autorisent PAS la Documentation
- ❌ "Termine le projet" (ne pas créer de docs automatiquement)
- ❌ "Finalise le système" (ne pas créer de résumés)
- ❌ "Organise les fichiers" (ne pas créer de guides d'organisation)
- ❌ "Améliore le code" (ne pas créer de documentation technique)

## 🎯 FOCUS SUR LE CODE ET LA FONCTIONNALITÉ

### Priorités OBLIGATOIRES
1. **Code fonctionnel** avant tout
2. **Tests qui passent** avant documentation
3. **Fonctionnalités qui marchent** avant explications
4. **Problèmes résolus** avant guides

### Réponses Appropriées
Au lieu de créer de la documentation automatiquement :
- ✅ **Corriger** les bugs trouvés
- ✅ **Améliorer** les fonctionnalités existantes
- ✅ **Optimiser** les performances
- ✅ **Tester** que tout fonctionne
- ✅ **Répondre** directement aux questions techniques

## 🚨 CONSÉQUENCES DE LA VIOLATION

**PROBLÈMES** causés par la création automatique de documentation :
- 🔥 **Pollution** : Trop de fichiers inutiles
- 🔥 **Confusion** : Documentation redondante ou obsolète
- 🔥 **Perte de temps** : Écriture non demandée
- 🔥 **Maintenance** : Plus de fichiers à maintenir
- 🔥 **Désorganisation** : Accumulation de documents

## 📋 CHECKLIST OBLIGATOIRE

Avant de créer TOUT fichier de documentation :

- [ ] **L'utilisateur a-t-il explicitement demandé cette documentation ?**
- [ ] **A-t-il utilisé des mots comme "créer", "écrire", "documenter" ?**
- [ ] **Est-ce vraiment nécessaire pour répondre à sa demande ?**
- [ ] **Ne puis-je pas répondre directement sans créer de fichier ?**

### Si TOUTES les réponses ne sont pas "OUI" → **NE PAS CRÉER**

## 🛠️ DÉVELOPPEMENT SANS DOCUMENTATION AUTOMATIQUE

### Quand l'utilisateur dit :
- "Termine le projet" → **Corriger les bugs, tester, optimiser**
- "Finalise le système" → **Valider que tout fonctionne**
- "Organise le code" → **Restructurer, nettoyer, optimiser**
- "Améliore le système" → **Ajouter des fonctionnalités, corriger**

### Ne PAS créer automatiquement :
- ❌ README_final.md
- ❌ SUMMARY_complete.md
- ❌ ORGANIZATION_guide.md
- ❌ FINAL_documentation.md

## 🎯 OBJECTIF FINAL

**Maintenir un système qui :**
- 🎯 **Se concentre sur le code** et la fonctionnalité
- 🛡️ **Évite la pollution documentaire** non demandée
- 🔍 **Répond directement** aux questions sans créer de fichiers
- 📊 **Garde seulement** la documentation vraiment utile

### 💡 Principe de Réponse Directe

Au lieu de créer un fichier de documentation, **répondre directement** dans la conversation avec les informations demandées.

---

**RAPPEL CRITIQUE** : La documentation non demandée pollue le projet et fait perdre du temps. Se concentrer sur le code fonctionnel et répondre directement aux questions de l'utilisateur.