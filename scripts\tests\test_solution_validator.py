"""
Tests unitaires pour le validateur de solution ARC AGI.

Ce module teste toutes les fonctionnalités du SolutionValidator,
incluant la validation pixel par pixel, la génération de grilles
de diagnostic et le calcul des métriques de précision.
"""

import unittest
import numpy as np
from unittest.mock import patch, MagicMock
import logging

# Import du module à tester
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from core.solution_validator import (
    SolutionValidator, ValidationResult, SolutionValidationError
)
from core.constants import (
    ValidationThresholds, SystemLimits, ColorCodes
)


class TestSolutionValidator(unittest.TestCase):
    """Tests pour la classe SolutionValidator."""
    
    def setUp(self):
        """Configuration avant chaque test."""
        self.validator = SolutionValidator()
        
        # Grilles de test simples
        self.correct_grid = np.array([
            [0, 1, 2],
            [3, 4, 5],
            [6, 7, 8]
        ])
        
        self.identical_grid = np.array([
            [0, 1, 2],
            [3, 4, 5],
            [6, 7, 8]
        ])
        
        self.partially_correct_grid = np.array([
            [0, 1, 2],
            [3, 9, 5],  # Erreur: 9 au lieu de 4
            [6, 7, 8]
        ])
        
        self.completely_wrong_grid = np.array([
            [9, 8, 7],
            [6, 5, 4],
            [3, 2, 1]
        ])
        
        # Grilles de tailles différentes pour les tests d'erreur
        self.wrong_size_grid = np.array([
            [0, 1],
            [2, 3]
        ])
    
    def test_validate_solution_perfect_match(self):
        """Test de validation avec une solution parfaite."""
        result = self.validator.validate_solution(self.identical_grid, self.correct_grid)
        
        self.assertIsInstance(result, ValidationResult)
        self.assertTrue(result.is_correct)
        self.assertEqual(result.accuracy_percentage, 100.0)
        self.assertEqual(result.total_errors, 0)
        self.assertEqual(len(result.error_positions), 0)
        self.assertEqual(result.correct_pixels, 9)
        self.assertEqual(result.total_pixels, 9)
    
    def test_validate_solution_partial_match(self):
        """Test de validation avec une solution partiellement correcte."""
        result = self.validator.validate_solution(self.partially_correct_grid, self.correct_grid)
        
        self.assertIsInstance(result, ValidationResult)
        self.assertFalse(result.is_correct)
        self.assertAlmostEqual(result.accuracy_percentage, 88.89, places=2)  # 8/9 * 100
        self.assertEqual(result.total_errors, 1)
        self.assertEqual(len(result.error_positions), 1)
        self.assertEqual(result.error_positions[0], (1, 1))  # Position de l'erreur
        self.assertEqual(result.correct_pixels, 8)
        self.assertEqual(result.total_pixels, 9)
    
    def test_validate_solution_complete_mismatch(self):
        """Test de validation avec une solution complètement incorrecte."""
        result = self.validator.validate_solution(self.completely_wrong_grid, self.correct_grid)
        
        self.assertIsInstance(result, ValidationResult)
        self.assertFalse(result.is_correct)
        self.assertEqual(result.accuracy_percentage, 0.0)
        self.assertEqual(result.total_errors, 9)
        self.assertEqual(len(result.error_positions), 9)
        self.assertEqual(result.correct_pixels, 0)
        self.assertEqual(result.total_pixels, 9)
    
    def test_validate_solution_dimension_mismatch(self):
        """Test de validation avec des dimensions incompatibles."""
        with self.assertRaises(SolutionValidationError) as context:
            self.validator.validate_solution(self.wrong_size_grid, self.correct_grid)
        
        self.assertIn("Dimensions incompatibles", str(context.exception))
    
    def test_validate_solution_invalid_input_types(self):
        """Test de validation avec des types d'entrée invalides."""
        # Test avec une liste au lieu d'un numpy array
        with self.assertRaises(SolutionValidationError):
            self.validator.validate_solution([[0, 1], [2, 3]], self.correct_grid)
        
        # Test avec None
        with self.assertRaises(SolutionValidationError):
            self.validator.validate_solution(None, self.correct_grid)
    
    def test_validate_solution_empty_grids(self):
        """Test de validation avec des grilles vides."""
        empty_grid = np.array([])
        
        with self.assertRaises(SolutionValidationError) as context:
            self.validator.validate_solution(empty_grid, empty_grid)
        
        self.assertIn("ne peuvent pas être vides", str(context.exception))
    
    def test_validate_solution_invalid_colors(self):
        """Test de validation avec des couleurs invalides."""
        invalid_grid = np.array([
            [0, 1, 15],  # 15 n'est pas une couleur ARC valide
            [3, 4, 5],
            [6, 7, 8]
        ])
        
        with self.assertRaises(SolutionValidationError) as context:
            self.validator.validate_solution(invalid_grid, self.correct_grid)
        
        self.assertIn("valeurs invalides", str(context.exception))
    
    def test_validate_solution_oversized_grid(self):
        """Test de validation avec une grille trop grande."""
        # Créer une grille plus grande que la limite
        oversized_grid = np.zeros((SystemLimits.MAX_GRID_SIZE + 1, SystemLimits.MAX_GRID_SIZE + 1))
        
        with self.assertRaises(SolutionValidationError) as context:
            self.validator.validate_solution(oversized_grid, oversized_grid)
        
        self.assertIn("Taille de grille invalide", str(context.exception))
    
    def test_generate_diagnostic_grid_perfect_match(self):
        """Test de génération de grille de diagnostic pour une solution parfaite."""
        diagnostic = self.validator.generate_diagnostic_grid(self.identical_grid, self.correct_grid)
        
        self.assertEqual(diagnostic.shape, self.correct_grid.shape)
        self.assertTrue(np.all(diagnostic == 'T'))
    
    def test_generate_diagnostic_grid_partial_match(self):
        """Test de génération de grille de diagnostic pour une solution partielle."""
        diagnostic = self.validator.generate_diagnostic_grid(self.partially_correct_grid, self.correct_grid)
        
        self.assertEqual(diagnostic.shape, self.correct_grid.shape)
        
        # Vérifier que seule la position (1,1) est marquée comme 'F'
        expected_diagnostic = np.array([
            ['T', 'T', 'T'],
            ['T', 'F', 'T'],
            ['T', 'T', 'T']
        ])
        
        np.testing.assert_array_equal(diagnostic, expected_diagnostic)
    
    def test_generate_diagnostic_grid_complete_mismatch(self):
        """Test de génération de grille de diagnostic pour une solution complètement incorrecte."""
        diagnostic = self.validator.generate_diagnostic_grid(self.completely_wrong_grid, self.correct_grid)
        
        self.assertEqual(diagnostic.shape, self.correct_grid.shape)
        self.assertTrue(np.all(diagnostic == 'F'))
    
    def test_generate_diagnostic_grid_dimension_mismatch(self):
        """Test de génération de grille de diagnostic avec des dimensions incompatibles."""
        with self.assertRaises(SolutionValidationError):
            self.validator.generate_diagnostic_grid(self.wrong_size_grid, self.correct_grid)
    
    def test_error_statistics_calculation(self):
        """Test du calcul des statistiques d'erreur."""
        # Créer une grille avec des erreurs spécifiques
        error_grid = np.array([
            [0, 1, 9],  # Erreur: 9 au lieu de 2
            [3, 9, 5],  # Erreur: 9 au lieu de 4
            [6, 7, 9]   # Erreur: 9 au lieu de 8
        ])
        
        result = self.validator.validate_solution(error_grid, self.correct_grid)
        
        # Vérifier les statistiques d'erreur
        self.assertIn('error_count_by_color', result.error_statistics)
        self.assertIn('most_common_errors', result.error_statistics)
        self.assertIn('error_distribution', result.error_statistics)
        self.assertIn('accuracy_level', result.error_statistics)
        
        # Vérifier que la couleur 9 apparaît 3 fois dans les erreurs
        self.assertEqual(result.error_statistics['error_count_by_color'][9], 3)
        
        # Vérifier le niveau de précision (66.7% tombe dans FAIBLE car < 80%)
        self.assertEqual(result.error_statistics['accuracy_level'], 'FAIBLE')  # 66.7% de précision
    
    def test_accuracy_levels(self):
        """Test des différents niveaux de précision."""
        # Test pour différents pourcentages de précision basés sur les seuils réels
        # PERFECT_ACCURACY = 100.0, HIGH_ACCURACY = 95.0, MEDIUM_ACCURACY = 80.0, LOW_ACCURACY = 50.0
        test_cases = [
            (0, 100, "TRÈS_FAIBLE"),    # 0% précision
            (40, 100, "TRÈS_FAIBLE"),   # 40% précision
            (60, 100, "FAIBLE"),        # 60% précision
            (85, 100, "MOYEN"),         # 85% précision
            (96, 100, "ÉLEVÉ"),         # 96% précision
            (100, 100, "PARFAIT")       # 100% précision
        ]
        
        for correct_pixels, total_pixels, expected_level in test_cases:
            error_count = total_pixels - correct_pixels
            level = self.validator._get_accuracy_level(error_count, total_pixels)
            self.assertEqual(level, expected_level)
    
    def test_format_diagnostic_grid(self):
        """Test du formatage de la grille de diagnostic."""
        diagnostic = np.array([
            ['T', 'T', 'F'],
            ['F', 'T', 'T'],
            ['T', 'F', 'F']
        ])
        
        formatted = self.validator.format_diagnostic_grid(diagnostic)
        
        self.assertIn("Grille de diagnostic", formatted)
        self.assertIn("T=correct", formatted)
        self.assertIn("F=incorrect", formatted)
        self.assertIn("T T F", formatted)
        self.assertIn("F T T", formatted)
        self.assertIn("T F F", formatted)
    
    def test_format_diagnostic_grid_none(self):
        """Test du formatage avec une grille de diagnostic None."""
        formatted = self.validator.format_diagnostic_grid(None)
        self.assertEqual(formatted, "Grille de diagnostic non disponible")
    
    def test_get_validation_summary_success(self):
        """Test du résumé de validation pour un succès."""
        result = self.validator.validate_solution(self.identical_grid, self.correct_grid)
        summary = self.validator.get_validation_summary(result)
        
        self.assertIn("✅ SUCCÈS", summary)
        self.assertIn("Puzzle résolu", summary)
        self.assertIn("100.0%", summary)
        self.assertIn("9/9", summary)
    
    def test_get_validation_summary_failure(self):
        """Test du résumé de validation pour un échec."""
        result = self.validator.validate_solution(self.partially_correct_grid, self.correct_grid)
        summary = self.validator.get_validation_summary(result)
        
        self.assertIn("❌ ÉCHEC", summary)
        self.assertIn("Solution incorrecte", summary)
        self.assertIn("88.9%", summary)
        self.assertIn("8/9", summary)
        self.assertIn("Erreurs: 1", summary)
    
    def test_validator_configuration(self):
        """Test de la configuration du validateur."""
        config = {
            'generate_diagnostic': False,
            'calculate_statistics': False,
            'save_results': False
        }
        
        validator = SolutionValidator(config)
        result = validator.validate_solution(self.partially_correct_grid, self.correct_grid)
        
        # Vérifier que la grille de diagnostic n'est pas générée
        self.assertIsNone(result.diagnostic_grid)
        
        # Vérifier que les statistiques ne sont pas calculées
        self.assertEqual(result.error_statistics, {})
    
    def test_large_grid_validation(self):
        """Test de validation avec une grande grille."""
        # Créer une grille de taille maximale autorisée
        size = SystemLimits.MAX_GRID_SIZE
        large_correct = np.random.randint(0, 10, (size, size))
        large_proposed = large_correct.copy()
        
        # Introduire quelques erreurs
        large_proposed[0, 0] = (large_proposed[0, 0] + 1) % 10
        large_proposed[size-1, size-1] = (large_proposed[size-1, size-1] + 1) % 10
        
        result = self.validator.validate_solution(large_proposed, large_correct)
        
        self.assertFalse(result.is_correct)
        self.assertEqual(result.total_errors, 2)
        self.assertEqual(result.total_pixels, size * size)
        self.assertEqual(len(result.error_positions), 2)
    
    def test_single_pixel_grid(self):
        """Test de validation avec une grille d'un seul pixel."""
        single_correct = np.array([[5]])
        single_proposed_correct = np.array([[5]])
        single_proposed_wrong = np.array([[3]])
        
        # Test avec solution correcte
        result_correct = self.validator.validate_solution(single_proposed_correct, single_correct)
        self.assertTrue(result_correct.is_correct)
        self.assertEqual(result_correct.accuracy_percentage, 100.0)
        
        # Test avec solution incorrecte
        result_wrong = self.validator.validate_solution(single_proposed_wrong, single_correct)
        self.assertFalse(result_wrong.is_correct)
        self.assertEqual(result_wrong.accuracy_percentage, 0.0)
        self.assertEqual(result_wrong.total_errors, 1)
    
    @patch('scripts.core.solution_validator.logging.getLogger')
    def test_logging_integration(self, mock_logger):
        """Test de l'intégration avec le système de logging."""
        mock_logger_instance = MagicMock()
        mock_logger.return_value = mock_logger_instance
        
        validator = SolutionValidator()
        validator.validate_solution(self.identical_grid, self.correct_grid)
        
        # Vérifier que le logging a été appelé
        mock_logger_instance.info.assert_called()
    
    def test_error_distribution_calculation(self):
        """Test du calcul de la distribution spatiale des erreurs."""
        # Créer une grille 4x4 avec des erreurs dans chaque quadrant
        correct_4x4 = np.array([
            [0, 1, 2, 3],
            [4, 5, 6, 7],
            [8, 9, 0, 1],
            [2, 3, 4, 5]
        ])
        
        error_4x4 = np.array([
            [9, 1, 2, 9],  # Erreurs en (0,0) et (0,3)
            [4, 5, 6, 7],
            [8, 9, 0, 9],  # Erreur en (2,3)
            [9, 3, 4, 5]   # Erreur en (3,0)
        ])
        
        result = self.validator.validate_solution(error_4x4, correct_4x4)
        
        distribution = result.error_statistics['error_distribution']
        
        # Vérifier la distribution dans les quadrants
        self.assertEqual(distribution['top_left'], 1)     # (0,0)
        self.assertEqual(distribution['top_right'], 1)    # (0,3)
        self.assertEqual(distribution['bottom_left'], 1)  # (3,0)
        self.assertEqual(distribution['bottom_right'], 1) # (2,3)


class TestValidationResultDataclass(unittest.TestCase):
    """Tests pour la dataclass ValidationResult."""
    
    def test_validation_result_creation(self):
        """Test de création d'un ValidationResult."""
        diagnostic_grid = np.array([['T', 'F'], ['T', 'T']])
        error_positions = [(0, 1)]
        error_statistics = {'accuracy_level': 'ÉLEVÉ'}
        
        result = ValidationResult(
            is_correct=False,
            accuracy_percentage=75.0,
            diagnostic_grid=diagnostic_grid,
            total_errors=1,
            error_positions=error_positions,
            correct_pixels=3,
            total_pixels=4,
            error_statistics=error_statistics
        )
        
        self.assertFalse(result.is_correct)
        self.assertEqual(result.accuracy_percentage, 75.0)
        self.assertEqual(result.total_errors, 1)
        self.assertEqual(result.error_positions, error_positions)
        self.assertEqual(result.correct_pixels, 3)
        self.assertEqual(result.total_pixels, 4)
        np.testing.assert_array_equal(result.diagnostic_grid, diagnostic_grid)
        self.assertEqual(result.error_statistics, error_statistics)


class TestSolutionValidationError(unittest.TestCase):
    """Tests pour l'exception SolutionValidationError."""
    
    def test_exception_creation(self):
        """Test de création de l'exception."""
        message = "Test error message"
        error = SolutionValidationError(message)
        
        self.assertEqual(str(error), message)
        self.assertIsInstance(error, Exception)


if __name__ == '__main__':
    # Configuration du logging pour les tests
    logging.basicConfig(level=logging.DEBUG)
    
    # Exécution des tests
    unittest.main(verbosity=2)