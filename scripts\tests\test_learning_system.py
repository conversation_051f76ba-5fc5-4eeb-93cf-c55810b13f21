#!/usr/bin/env python3
"""
Tests pour le système d'apprentissage ARC AGI.

Ce module teste l'extraction et l'intégration d'insights, la validation
de généricité et la prévention de contamination croisée.
"""

import unittest
import json
import tempfile
import os
import sys
from pathlib import Path
from datetime import datetime
from unittest.mock import patch, MagicMock
import numpy as np

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from core.learning_system import LearningSystem
from core.data_models import AnalysisResult, ValidationResult, ParsedResponse, LearningInsight


class TestLearningSystem(unittest.TestCase):
    """Tests pour la classe LearningSystem."""
    
    def setUp(self):
        """Configuration avant chaque test."""
        self.learning_system = LearningSystem()
        self.test_timestamp = datetime.now()
        
        # Données de test pour une analyse réussie
        self.successful_analysis = {
            "puzzle_id": "test_001",
            "analysis_data": {
                "raw_analysis": {
                    "transformations": {
                        "color": {"color_removal": True},
                        "structural": {"in_place_transformation": True}
                    },
                    "diff_analysis": {
                        "common_color_changes": {"3→4": 5, "1→2": 3},
                        "train_diffs": [
                            {"change_percentage": 5.2, "total_changes": 12},
                            {"change_percentage": 4.8, "total_changes": 10}
                        ]
                    },
                    "objects": {
                        "input_objects": [
                            [{"size": 1}, {"size": 10}],
                            [{"size": 2}, {"size": 8}]
                        ]
                    },
                    "spatial_relations": {
                        "alignment_patterns": [
                            {"horizontal": 3, "vertical": 1},
                            {"horizontal": 2, "vertical": 2}
                        ]
                    }
                }
            }
        }
        
        # Résultat d'analyse réussie
        self.analysis_result = AnalysisResult(
            puzzle_id="test_001",
            success=True,
            proposed_solution=np.array([[1, 2], [3, 4]]),
            validation_result=ValidationResult(
                is_correct=True,
                accuracy_percentage=100.0,
                diagnostic_grid=np.array([['T', 'T'], ['T', 'T']]),
                total_errors=0,
                error_positions=[]
            ),
            ai_analysis=ParsedResponse(
                transformations=["color_removal"],
                patterns=["horizontal_alignment"],
                rules=["transform_color_3_to_4"],
                proposed_grid=np.array([[1, 2], [3, 4]]),
                reasoning_steps=["Step 1", "Step 2"],
                interpretation="Test interpretation",
                raw_response="Test response"
            ),
            execution_time=1.5,
            saved_to="test_path.txt",
            timestamp=self.test_timestamp
        )
    
    def test_propose_learning_success(self):
        """Test de la proposition d'apprentissage pour une analyse réussie."""
        with patch('builtins.input', return_value='o'):
            result = self.learning_system.propose_learning(self.analysis_result)
            self.assertTrue(result)
    
    def test_propose_learning_declined(self):
        """Test de la proposition d'apprentissage déclinée."""
        with patch('builtins.input', return_value='n'):
            result = self.learning_system.propose_learning(self.analysis_result)
            self.assertFalse(result)
    
    def test_propose_learning_failed_analysis(self):
        """Test de la proposition d'apprentissage pour une analyse échouée."""
        failed_result = AnalysisResult(
            puzzle_id="test_failed",
            success=False,
            proposed_solution=None,
            validation_result=None,
            ai_analysis=None,
            execution_time=1.0,
            saved_to=None,
            timestamp=self.test_timestamp
        )
        
        result = self.learning_system.propose_learning(failed_result)
        self.assertFalse(result)
    
    def test_extract_raw_insights(self):
        """Test de l'extraction d'insights bruts."""
        insights = self.learning_system._extract_raw_insights(self.successful_analysis)
        
        self.assertIsNotNone(insights)
        self.assertIn("critical_observations", insights)
        self.assertIn("color_transformations", insights)
        self.assertIn("object_structure", insights)
        
        # Vérifier que des observations ont été extraites
        observations = insights.get("critical_observations", [])
        self.assertGreater(len(observations), 0)
    
    def test_extract_raw_insights_missing_data(self):
        """Test de l'extraction avec données manquantes."""
        incomplete_analysis = {"puzzle_id": "test_incomplete"}
        
        insights = self.learning_system._extract_raw_insights(incomplete_analysis)
        self.assertIsNone(insights)
    
    def test_validate_insight_genericity_valid(self):
        """Test de validation de généricité avec insights valides."""
        valid_insights = {
            "critical_observations": [
                "COULEUR CIBLE: Transformation de couleur détectée avec 5 occurrences",
                "CHANGEMENTS: Très peu de pixels modifiés (5.0% max) - transformation locale"
            ],
            "color_transformations": {"changes": {"3→4": 5}},
            "change_statistics": {"low_change_rate": True}
        }
        
        result = self.learning_system._validate_insight_genericity(valid_insights, "test_001")
        self.assertTrue(result)
    
    def test_validate_insight_genericity_forbidden_keywords(self):
        """Test de validation avec mots-clés interdits."""
        invalid_insights = {
            "critical_observations": [
                "Règle de proximité aux bordures détectée",
                "Distance Manhattan utilisée pour le calcul"
            ]
        }
        
        result = self.learning_system._validate_insight_genericity(invalid_insights, "test_001")
        self.assertFalse(result)
    
    def test_validate_insight_genericity_no_data_based_observations(self):
        """Test de validation sans observations basées sur les données."""
        invalid_insights = {
            "critical_observations": [
                "Quelque chose se passe",
                "Il faut faire attention"
            ]
        }
        
        result = self.learning_system._validate_insight_genericity(invalid_insights, "test_001")
        self.assertFalse(result)
    
    def test_structure_insights(self):
        """Test de structuration des insights."""
        raw_insights = {
            "critical_observations": [
                "COULEUR CIBLE: Transformation détectée",
                "STRUCTURE: Objets de tailles différentes"
            ],
            "color_transformations": {"total_transformations": 5},
            "object_structure": {"size_variety": 2},
            "change_statistics": {"low_change_rate": True}
        }
        
        structured = self.learning_system._structure_insights(raw_insights, "test_001")
        
        self.assertEqual(len(structured), 2)
        self.assertIsInstance(structured[0], LearningInsight)
        self.assertEqual(structured[0].source_puzzle, "test_001")
        self.assertGreater(structured[0].confidence, 0)
        self.assertLessEqual(structured[0].confidence, 1)
    
    def test_classify_insight_type(self):
        """Test de classification des types d'insights."""
        test_cases = [
            ("COULEUR CIBLE: Transformation détectée", "color_transformation"),
            ("STRUCTURE: Objets détectés", "object_structure"),
            ("ALIGNEMENT: Pattern horizontal", "spatial_pattern"),
            ("TRANSFORMATION: Modification sur place", "transformation_type"),
            ("CHANGEMENTS: Pixels modifiés", "change_pattern"),
            ("MOTIF: Pattern détecté", "motif_detection"),
            ("Observation générale", "general_observation")
        ]
        
        for observation, expected_type in test_cases:
            result = self.learning_system._classify_insight_type(observation)
            self.assertEqual(result, expected_type)
    
    def test_extract_conditions(self):
        """Test d'extraction des conditions d'application."""
        raw_insights = {
            "color_transformations": {"total_transformations": 5},
            "object_structure": {"size_variety": 3},
            "change_statistics": {"low_change_rate": True},
            "spatial_patterns": {
                "has_horizontal_borders": True,
                "has_vertical_borders": False
            }
        }
        
        conditions = self.learning_system._extract_conditions("test observation", raw_insights)
        
        expected_conditions = [
            "color_transformations_detected",
            "multiple_object_sizes",
            "low_change_rate",
            "horizontal_alignment_detected"
        ]
        
        for condition in expected_conditions:
            self.assertIn(condition, conditions)
    
    def test_calculate_confidence(self):
        """Test de calcul de confiance."""
        raw_insights = {
            "color_transformations": {"changes": {"3→4": 5}},
            "object_structure": {"size_variety": 2},
            "change_statistics": {"low_change_rate": True}
        }
        
        # Observation avec données quantitatives
        confidence1 = self.learning_system._calculate_confidence(
            "5 occurrences détectées", raw_insights
        )
        self.assertGreater(confidence1, 0.5)
        
        # Observation sans données quantitatives
        confidence2 = self.learning_system._calculate_confidence(
            "Pattern détecté", raw_insights
        )
        self.assertGreaterEqual(confidence2, 0.5)
        self.assertLessEqual(confidence2, 1.0)
    
    def test_integrate_insights(self):
        """Test d'intégration des insights."""
        insights = [
            LearningInsight(
                insight_type="color_transformation",
                description="Test insight 1",
                conditions=["color_transformations_detected"],
                confidence=0.8,
                source_puzzle="test_001",
                extracted_at=self.test_timestamp
            ),
            LearningInsight(
                insight_type="object_structure",
                description="Test insight 2",
                conditions=["multiple_object_sizes"],
                confidence=0.7,
                source_puzzle="test_001",
                extracted_at=self.test_timestamp
            )
        ]
        
        result = self.learning_system._integrate_insights(insights)
        self.assertTrue(result)
        
        # Vérifier que les insights sont dans le cache
        self.assertEqual(len(self.learning_system.insights_cache), 2)
        
        # Vérifier les clés du cache
        expected_keys = [
            "color_transformation_test_001",
            "object_structure_test_001"
        ]
        for key in expected_keys:
            self.assertIn(key, self.learning_system.insights_cache)
    
    def test_get_applicable_insights(self):
        """Test de récupération d'insights applicables."""
        # Ajouter des insights au cache
        insight1 = LearningInsight(
            insight_type="color_transformation",
            description="Color insight",
            conditions=["color_transformations_detected"],
            confidence=0.9,
            source_puzzle="test_001",
            extracted_at=self.test_timestamp
        )
        
        insight2 = LearningInsight(
            insight_type="object_structure",
            description="Structure insight",
            conditions=["multiple_object_sizes"],
            confidence=0.7,
            source_puzzle="test_002",
            extracted_at=self.test_timestamp
        )
        
        self.learning_system.insights_cache = {
            "color_transformation_test_001": insight1,
            "object_structure_test_002": insight2
        }
        
        # Contexte qui correspond au premier insight
        context = {
            "has_color_transformations": True,
            "object_size_variety": 1
        }
        
        applicable = self.learning_system.get_applicable_insights(context)
        
        self.assertEqual(len(applicable), 1)
        self.assertEqual(applicable[0].insight_type, "color_transformation")
    
    def test_check_condition(self):
        """Test de vérification des conditions."""
        context = {
            "has_color_transformations": True,
            "object_size_variety": 3,
            "change_percentage": 5.0
        }
        
        # Test des différentes conditions
        self.assertTrue(
            self.learning_system._check_condition("color_transformations_detected", context)
        )
        self.assertTrue(
            self.learning_system._check_condition("multiple_object_sizes", context)
        )
        self.assertTrue(
            self.learning_system._check_condition("low_change_rate", context)
        )
        
        # Test d'une condition non remplie
        context_no_color = {"has_color_transformations": False}
        self.assertFalse(
            self.learning_system._check_condition("color_transformations_detected", context_no_color)
        )
    
    def test_extract_and_integrate_insights_complete_workflow(self):
        """Test du workflow complet d'extraction et intégration."""
        result = self.learning_system.extract_and_integrate_insights(self.successful_analysis)
        
        self.assertTrue(result)
        self.assertGreater(len(self.learning_system.insights_cache), 0)
    
    def test_extract_and_integrate_insights_invalid_data(self):
        """Test du workflow avec données invalides."""
        invalid_analysis = {"puzzle_id": "invalid"}
        
        result = self.learning_system.extract_and_integrate_insights(invalid_analysis)
        self.assertFalse(result)
    
    def test_save_insights_to_disk(self):
        """Test de sauvegarde des insights sur disque."""
        insights = [
            LearningInsight(
                insight_type="test_type",
                description="Test description",
                conditions=["test_condition"],
                confidence=0.8,
                source_puzzle="test_001",
                extracted_at=self.test_timestamp
            )
        ]
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Changer temporairement le répertoire de sauvegarde
            original_cwd = os.getcwd()
            os.chdir(temp_dir)
            
            try:
                self.learning_system._save_insights_to_disk(insights)
                
                # Vérifier que le fichier a été créé
                insights_dir = Path("analysis_data/insights")
                self.assertTrue(insights_dir.exists())
                
                expected_file = insights_dir / "insight_test_001_test_type.json"
                self.assertTrue(expected_file.exists())
                
                # Vérifier le contenu
                with open(expected_file, 'r', encoding='utf-8') as f:
                    saved_data = json.load(f)
                
                self.assertEqual(saved_data["insight_type"], "test_type")
                self.assertEqual(saved_data["source_puzzle"], "test_001")
                self.assertEqual(saved_data["confidence"], 0.8)
                
            finally:
                os.chdir(original_cwd)


class TestLearningSystemSafety(unittest.TestCase):
    """Tests de sécurité pour prévenir la contamination croisée."""
    
    def setUp(self):
        """Configuration avant chaque test."""
        self.learning_system = LearningSystem()
    
    def test_no_cross_contamination_between_puzzles(self):
        """Test qu'il n'y a pas de contamination croisée entre puzzles."""
        # Créer deux analyses différentes
        analysis1 = {
            "puzzle_id": "puzzle_001",
            "analysis_data": {
                "raw_analysis": {
                    "diff_analysis": {
                        "common_color_changes": {"1→2": 3},
                        "train_diffs": [{"change_percentage": 10.0}]
                    }
                }
            }
        }
        
        analysis2 = {
            "puzzle_id": "puzzle_002", 
            "analysis_data": {
                "raw_analysis": {
                    "diff_analysis": {
                        "common_color_changes": {"3→4": 5},
                        "train_diffs": [{"change_percentage": 5.0}]
                    }
                }
            }
        }
        
        # Extraire les insights des deux puzzles
        insights1 = self.learning_system._extract_raw_insights(analysis1)
        insights2 = self.learning_system._extract_raw_insights(analysis2)
        
        # Vérifier que les insights sont différents
        self.assertNotEqual(insights1, insights2)
        
        # Vérifier qu'aucun insight ne référence l'autre puzzle
        insights1_text = json.dumps(insights1, ensure_ascii=False).lower()
        insights2_text = json.dumps(insights2, ensure_ascii=False).lower()
        
        self.assertNotIn("puzzle_002", insights1_text)
        self.assertNotIn("puzzle_001", insights2_text)
    
    def test_forbidden_keywords_detection(self):
        """Test de détection des mots-clés interdits selon arc_insights_safety."""
        forbidden_test_cases = [
            {"critical_observations": ["Distance Manhattan utilisée"]},
            {"critical_observations": ["Proximité aux bordures détectée"]},
            {"critical_observations": ["Taille ≥10 requise"]},
            {"critical_observations": ["Déplacement vers le bas observé"]},
            {"critical_observations": ["Position (3,4) importante"]},
            {"critical_observations": ["Comme dans le puzzle 001"]}
        ]
        
        for forbidden_insights in forbidden_test_cases:
            result = self.learning_system._validate_insight_genericity(
                forbidden_insights, "test_puzzle"
            )
            self.assertFalse(result, f"Mots-clés interdits non détectés: {forbidden_insights}")
    
    def test_generic_insights_acceptance(self):
        """Test d'acceptation d'insights génériques valides."""
        valid_test_cases = [
            {
                "critical_observations": [
                    "COULEUR CIBLE: Transformation détectée avec 5 occurrences",
                    "CHANGEMENTS: 4.7% des pixels modifiés en moyenne"
                ]
            },
            {
                "critical_observations": [
                    "STRUCTURE: Objets de tailles variées détectés (max: 10, min: 1)",
                    "ALIGNEMENT: Patterns horizontaux détectés"
                ]
            }
        ]
        
        for valid_insights in valid_test_cases:
            result = self.learning_system._validate_insight_genericity(
                valid_insights, "test_puzzle"
            )
            self.assertTrue(result, f"Insights valides rejetés: {valid_insights}")
    
    def test_data_based_observations_requirement(self):
        """Test que les observations doivent être basées sur les données."""
        # Observations non basées sur les données
        non_data_based = {
            "critical_observations": [
                "Il faut faire attention",
                "Quelque chose d'important se passe",
                "C'est compliqué"
            ]
        }
        
        result = self.learning_system._validate_insight_genericity(
            non_data_based, "test_puzzle"
        )
        self.assertFalse(result)
        
        # Observations basées sur les données
        data_based = {
            "critical_observations": [
                "5 occurrences détectées dans l'analyse",
                "Transformation calculée sur 12 pixels",
                "Pattern extrait des données d'entrée"
            ]
        }
        
        result = self.learning_system._validate_insight_genericity(
            data_based, "test_puzzle"
        )
        self.assertTrue(result)
    
    def test_validation_disabled_bypass(self):
        """Test que la validation peut être désactivée pour les tests."""
        self.learning_system.validation_enabled = False
        
        # Même avec des mots-clés interdits, la validation devrait passer
        forbidden_insights = {
            "critical_observations": ["Distance Manhattan utilisée"]
        }
        
        result = self.learning_system._validate_insight_genericity(
            forbidden_insights, "test_puzzle"
        )
        self.assertTrue(result)
        
        # Remettre la validation
        self.learning_system.validation_enabled = True


class TestLearningSystemIntegration(unittest.TestCase):
    """Tests d'intégration pour le système d'apprentissage."""
    
    def setUp(self):
        """Configuration avant chaque test."""
        self.learning_system = LearningSystem()
    
    def test_full_learning_workflow_with_user_acceptance(self):
        """Test du workflow complet avec acceptation utilisateur."""
        # Simuler une analyse réussie complète
        successful_analysis = {
            "puzzle_id": "integration_test",
            "analysis_data": {
                "raw_analysis": {
                    "transformations": {"color": {"color_removal": True}},
                    "diff_analysis": {
                        "common_color_changes": {"2→3": 4},
                        "train_diffs": [{"change_percentage": 8.5}]
                    }
                }
            }
        }
        
        analysis_result = AnalysisResult(
            puzzle_id="integration_test",
            success=True,
            proposed_solution=np.array([[1, 2]]),
            validation_result=ValidationResult(
                is_correct=True,
                accuracy_percentage=100.0,
                diagnostic_grid=np.array([['T', 'T']]),
                total_errors=0,
                error_positions=[]
            ),
            ai_analysis=None,
            execution_time=1.0,
            saved_to="test.txt",
            timestamp=datetime.now()
        )
        
        # Simuler l'acceptation utilisateur
        with patch('builtins.input', return_value='o'):
            learning_accepted = self.learning_system.propose_learning(analysis_result)
            self.assertTrue(learning_accepted)
            
            # Exécuter l'extraction et l'intégration
            integration_success = self.learning_system.extract_and_integrate_insights(
                successful_analysis
            )
            self.assertTrue(integration_success)
            
            # Vérifier que des insights ont été ajoutés au cache
            self.assertGreater(len(self.learning_system.insights_cache), 0)
    
    def test_full_learning_workflow_with_user_rejection(self):
        """Test du workflow complet avec rejet utilisateur."""
        analysis_result = AnalysisResult(
            puzzle_id="rejection_test",
            success=True,
            proposed_solution=np.array([[1]]),
            validation_result=ValidationResult(
                is_correct=True,
                accuracy_percentage=100.0,
                diagnostic_grid=np.array([['T']]),
                total_errors=0,
                error_positions=[]
            ),
            ai_analysis=None,
            execution_time=1.0,
            saved_to="test.txt",
            timestamp=datetime.now()
        )
        
        # Simuler le rejet utilisateur
        with patch('builtins.input', return_value='n'):
            learning_accepted = self.learning_system.propose_learning(analysis_result)
            self.assertFalse(learning_accepted)
            
            # Le cache devrait rester vide
            self.assertEqual(len(self.learning_system.insights_cache), 0)


def run_all_tests():
    """Exécute tous les tests du système d'apprentissage."""
    print("🧪 Exécution des tests du système d'apprentissage...")
    
    # Créer la suite de tests
    test_suite = unittest.TestSuite()
    
    # Ajouter les tests de base
    test_suite.addTest(unittest.makeSuite(TestLearningSystem))
    
    # Ajouter les tests de sécurité
    test_suite.addTest(unittest.makeSuite(TestLearningSystemSafety))
    
    # Ajouter les tests d'intégration
    test_suite.addTest(unittest.makeSuite(TestLearningSystemIntegration))
    
    # Exécuter les tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Résumé des résultats
    print(f"\n📊 RÉSULTATS DES TESTS:")
    print(f"✅ Tests réussis: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ Tests échoués: {len(result.failures)}")
    print(f"💥 Erreurs: {len(result.errors)}")
    
    if result.failures:
        print(f"\n❌ ÉCHECS:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print(f"\n💥 ERREURS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print(f"\n🎉 TOUS LES TESTS RÉUSSIS!")
        print(f"✅ Le système d'apprentissage respecte les règles arc_insights_safety")
        print(f"✅ Aucune contamination croisée détectée")
        print(f"✅ Extraction et intégration d'insights fonctionnelles")
    else:
        print(f"\n⚠️  CERTAINS TESTS ONT ÉCHOUÉ")
        print(f"🔧 Corrections nécessaires avant utilisation")
    
    return success


if __name__ == "__main__":
    import sys
    success = run_all_tests()
    sys.exit(0 if success else 1)