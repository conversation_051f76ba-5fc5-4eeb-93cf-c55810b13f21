"""
Chargeur de variables d'environnement pour le système ARC AGI.

Ce module charge automatiquement les variables d'environnement depuis un fichier .env
et fournit des utilitaires pour accéder aux configurations.
"""

import os
from pathlib import Path
from typing import Optional, Dict, Any


def load_env_file(env_path: Optional[str] = None) -> bool:
    """
    Charge les variables d'environnement depuis un fichier .env.
    
    Args:
        env_path: Chemin vers le fichier .env (optionnel)
        
    Returns:
        bool: True si le fichier a été chargé avec succès
    """
    if env_path is None:
        # Chercher le fichier .env dans le répertoire racine du projet
        current_dir = Path(__file__).parent.parent.parent
        env_path = current_dir / '.env'
    
    env_path = Path(env_path)
    
    if not env_path.exists():
        return False
    
    try:
        with open(env_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                
                # Ignorer les lignes vides et les commentaires
                if not line or line.startswith('#'):
                    continue
                
                # Parser les variables KEY=VALUE
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    
                    # Enlever les guillemets si présents
                    if value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    elif value.startswith("'") and value.endswith("'"):
                        value = value[1:-1]
                    
                    # Ne pas écraser les variables déjà définies
                    if key not in os.environ:
                        os.environ[key] = value
        
        return True
        
    except Exception as e:
        print(f"Erreur lors du chargement du fichier .env: {e}")
        return False


def get_api_key(provider: str) -> Optional[str]:
    """
    Récupère la clé API pour un provider donné.
    
    Args:
        provider: Nom du provider ('openai', 'openrouter', 'huggingface')
        
    Returns:
        str: Clé API ou None si non trouvée
    """
    env_var_map = {
        'openai': 'OPENAI_API_KEY',
        'groq': 'GROQ_API_KEY',
        'openrouter': 'OPENROUTER_API_KEY', 
        'huggingface': 'HUGGINGFACE_API_KEY'
    }
    
    env_var = env_var_map.get(provider.lower())
    if not env_var:
        return None
    
    return os.getenv(env_var)


def get_default_model(provider: str) -> Optional[str]:
    """
    Récupère le modèle par défaut pour un provider donné.
    
    Args:
        provider: Nom du provider
        
    Returns:
        str: Nom du modèle par défaut ou None
    """
    env_var_map = {
        'openai': 'OPENAI_DEFAULT_MODEL',
        'openrouter': 'OPENROUTER_DEFAULT_MODEL',
        'huggingface': 'HUGGINGFACE_DEFAULT_MODEL',
        'ollama': 'OLLAMA_DEFAULT_MODEL'
    }
    
    env_var = env_var_map.get(provider.lower())
    if not env_var:
        return None
    
    return os.getenv(env_var)


def get_provider_config(provider: str) -> Dict[str, Any]:
    """
    Récupère la configuration complète pour un provider.
    
    Args:
        provider: Nom du provider
        
    Returns:
        dict: Configuration du provider
    """
    config = {}
    
    # Clé API
    api_key = get_api_key(provider)
    if api_key:
        config['api_key'] = api_key
    
    # Modèle par défaut
    model = get_default_model(provider)
    if model:
        config['model'] = model
    
    # Configuration spécifique à Ollama
    if provider.lower() == 'ollama':
        base_url = os.getenv('OLLAMA_BASE_URL')
        if base_url:
            config['base_url'] = base_url
    
    return config


def check_api_keys() -> Dict[str, bool]:
    """
    Vérifie quelles clés API sont disponibles.
    
    Returns:
        dict: Statut de chaque clé API
    """
    providers = ['openai', 'openrouter', 'huggingface']
    status = {}
    
    for provider in providers:
        api_key = get_api_key(provider)
        status[provider] = api_key is not None and api_key != f'your_{provider}_api_key_here'
    
    return status


def print_env_status():
    """Affiche le statut des variables d'environnement."""
    print("🔑 Statut des clés API:")
    print("-" * 30)
    
    status = check_api_keys()
    for provider, available in status.items():
        icon = "✅" if available else "❌"
        print(f"{icon} {provider.upper()}: {'Configurée' if available else 'Manquante'}")
    
    print("\n🎯 Modèles par défaut:")
    print("-" * 30)
    
    providers = ['openai', 'openrouter', 'huggingface', 'ollama']
    for provider in providers:
        model = get_default_model(provider)
        if model:
            print(f"🎯 {provider.upper()}: {model}")
        else:
            print(f"⚪ {provider.upper()}: Modèle par défaut du système")


# Charger automatiquement le fichier .env au moment de l'import
_env_loaded = load_env_file()

if not _env_loaded:
    # Créer un fichier .env template s'il n'existe pas
    current_dir = Path(__file__).parent.parent.parent
    env_template_path = current_dir / '.env'
    
    if not env_template_path.exists():
        print("ℹ️  Fichier .env non trouvé. Vous pouvez en créer un pour configurer vos clés API.")