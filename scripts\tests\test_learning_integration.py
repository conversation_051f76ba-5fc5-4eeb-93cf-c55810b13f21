#!/usr/bin/env python3
"""
Tests d'intégration pour le système d'apprentissage ARC AGI.

Ce module teste l'intégration complète du système d'apprentissage,
l'extraction et intégration d'insights bout en bout, et le respect
des règles de généricité selon arc_insights_safety.
"""

import unittest
import sys
import json
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import numpy as np
from datetime import datetime

# Ajouter le répertoire parent au path pour les imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.learning_system import LearningSystem
from core.automated_analyzer import AutomatedARCAnalyzer
from core.data_models import AnalysisResult, ValidationResult, ParsedResponse


class TestLearningIntegration(unittest.TestCase):
    """Tests d'intégration pour le système d'apprentissage complet."""
    
    def setUp(self):
        """Configuration des tests."""
        self.temp_dir = tempfile.mkdtemp()
        self.temp_path = Path(self.temp_dir)
        
        # Créer les répertoires nécessaires
        (self.temp_path / "analysis_data").mkdir(exist_ok=True)
        (self.temp_path / "arc_results").mkdir(exist_ok=True)
        
        # Mock des chemins
        self.original_cwd = Path.cwd()
        
        # Créer le système d'apprentissage
        self.learning_system = LearningSystem()
        
        # Données de test pour une analyse réussie
        self.successful_analysis = AnalysisResult(
            puzzle_id="test_001",
            success=True,
            proposed_solution=np.array([[1, 2], [3, 4]]),
            validation_result=ValidationResult(
                is_correct=True,
                accuracy_percentage=100.0,
                diagnostic_grid=np.array([['T', 'T'], ['T', 'T']]),
                total_errors=0,
                error_positions=[]
            ),
            ai_analysis=ParsedResponse(
                transformations=["color_change", "position_shift"],
                patterns=["horizontal_symmetry", "color_progression"],
                rules=["IF color=1 THEN change to 2", "WHEN position=(0,0) THEN shift right"],
                proposed_grid=np.array([[1, 2], [3, 4]]),
                reasoning_steps=[
                    "Analyzed input patterns",
                    "Detected color transformation rule",
                    "Applied rule to test input"
                ],
                interpretation="Grid shows color progression pattern",
                raw_response="Mock AI response with detailed analysis"
            ),
            execution_time=2.5,
            saved_to=f"{self.temp_dir}/arc_results/puzzle_test_001_success.txt",
            timestamp=datetime.now()
        )
    
    def tearDown(self):
        """Nettoyage après les tests."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_complete_learning_workflow(self):
        """Test du workflow complet d'apprentissage."""
        with patch('builtins.input', return_value='y'):
            # Tester la proposition d'apprentissage
            result = self.learning_system.propose_learning(self.successful_analysis)
            self.assertTrue(result)
        
        with patch('builtins.input', return_value='n'):
            # Tester le refus d'apprentissage
            result = self.learning_system.propose_learning(self.successful_analysis)
            self.assertFalse(result)
    
    def test_insight_extraction_and_integration(self):
        """Test de l'extraction et intégration d'insights."""
        # Créer des données d'analyse au format attendu par extract_and_integrate_insights
        analysis_data = {
            "puzzle_id": "test_001",
            "analysis_data": {
                "raw_analysis": {
                    "transformations": {"color": {"color_removal": True}},
                    "diff_analysis": {
                        "common_color_changes": {"3→4": 5},
                        "train_diffs": [{"change_percentage": 5.2}]
                    }
                }
            }
        }
        
        # Mock des insights extraits
        mock_insights = {
            "critical_observations": [
                "COULEUR CIBLE: Seule la couleur 3 est transformée dans tous les exemples",
                "CHANGEMENTS: Très peu de pixels modifiés (5.2% max) - transformation locale et ciblée"
            ],
            "color_transformations": {"changes": {"3→4": 5}},
            "change_statistics": {"low_change_rate": True}
        }
        
        with patch.object(self.learning_system, '_extract_raw_insights') as mock_extract:
            with patch.object(self.learning_system, '_validate_insight_genericity') as mock_validate:
                with patch.object(self.learning_system, '_integrate_insights') as mock_integrate:
                    mock_extract.return_value = mock_insights
                    mock_validate.return_value = True
                    mock_integrate.return_value = True
                    
                    result = self.learning_system.extract_and_integrate_insights(analysis_data)
                    
                    self.assertTrue(result)
                    mock_extract.assert_called_once_with(analysis_data)
                    mock_validate.assert_called_once_with(mock_insights, "test_001")
                    mock_integrate.assert_called_once()
    
    def test_genericity_validation_compliance(self):
        """Test du respect des règles de généricité arc_insights_safety."""
        # Insights génériques valides
        valid_insights = [
            "Pattern detected: Color transformation in 75% of examples",
            "Rule identified: IF input_color != background THEN apply_transformation",
            "Constraint found: Transformations preserve spatial relationships"
        ]
        
        # Insights non-génériques (interdits)
        invalid_insights = [
            "Rule: Distance Manhattan ≥ 10 triggers transformation",  # Valeur hardcodée
            "Pattern: Déplacement vers le bas dans 80% des cas",      # Direction spécifique
            "Constraint: Bordures fixes en position (0,0)",          # Position hardcodée
            "Similar to puzzle 123abc",                               # Référence croisée
        ]
        
        # Créer des données d'analyse au format attendu
        analysis_data = {
            "puzzle_id": "test_genericity",
            "analysis_data": {
                "raw_analysis": {
                    "transformations": {"color": {"color_removal": True}},
                    "diff_analysis": {
                        "common_color_changes": {"3→4": 5},
                        "train_diffs": [{"change_percentage": 5.2}]
                    }
                }
            }
        }
        
        with patch.object(self.learning_system, '_validate_insight_genericity') as mock_validate:
            with patch.object(self.learning_system, '_extract_raw_insights') as mock_extract:
                with patch.object(self.learning_system, '_integrate_insights') as mock_integrate:
                    mock_extract.return_value = {"critical_observations": ["Valid insight"]}
                    mock_integrate.return_value = True
                    
                    # Test avec insights valides
                    mock_validate.return_value = True
                    result = self.learning_system.extract_and_integrate_insights(analysis_data)
                    self.assertTrue(result)
                    
                    # Test avec insights invalides
                    mock_validate.return_value = False
                    result = self.learning_system.extract_and_integrate_insights(analysis_data)
                    self.assertFalse(result)
    
    def test_cross_contamination_prevention(self):
        """Test de la prévention de contamination croisée entre puzzles."""
        # Créer deux analyses de puzzles différents
        analysis_1 = self.successful_analysis
        analysis_2 = AnalysisResult(
            puzzle_id="test_002",
            success=True,
            proposed_solution=np.array([[5, 6], [7, 8]]),
            validation_result=ValidationResult(
                is_correct=True,
                accuracy_percentage=100.0,
                diagnostic_grid=np.array([['T', 'T'], ['T', 'T']]),
                total_errors=0,
                error_positions=[]
            ),
            ai_analysis=ParsedResponse(
                transformations=["rotation", "scaling"],
                patterns=["diagonal_symmetry", "size_doubling"],
                rules=["IF shape=square THEN rotate 90°", "WHEN size<3 THEN double"],
                proposed_grid=np.array([[5, 6], [7, 8]]),
                reasoning_steps=[
                    "Detected geometric transformation",
                    "Applied rotation rule",
                    "Verified size constraints"
                ],
                interpretation="Grid shows geometric progression",
                raw_response="Different AI response for puzzle 2"
            ),
            execution_time=3.1,
            saved_to=f"{self.temp_dir}/arc_results/puzzle_test_002_success.txt",
            timestamp=datetime.now()
        )
        
        # Créer des données d'analyse pour les deux puzzles
        analysis_data_1 = {
            "puzzle_id": "test_001",
            "analysis_data": {"raw_analysis": {"transformations": {"color": {"color_removal": True}}}}
        }
        analysis_data_2 = {
            "puzzle_id": "test_002", 
            "analysis_data": {"raw_analysis": {"transformations": {"geometric": {"rotation": True}}}}
        }
        
        # Mock pour s'assurer que chaque puzzle génère des insights différents
        def mock_extract_side_effect(analysis_data):
            if analysis_data["puzzle_id"] == "test_001":
                return {"critical_observations": ["Color-based transformation pattern", "Horizontal alignment rule"]}
            elif analysis_data["puzzle_id"] == "test_002":
                return {"critical_observations": ["Geometric transformation pattern", "Size-based scaling rule"]}
            return {}
        
        with patch.object(self.learning_system, '_extract_raw_insights', side_effect=mock_extract_side_effect):
            with patch.object(self.learning_system, '_validate_insight_genericity', return_value=True):
                with patch.object(self.learning_system, '_integrate_insights', return_value=True):
                    # Extraire insights du premier puzzle
                    result_1 = self.learning_system.extract_and_integrate_insights(analysis_data_1)
                    
                    # Extraire insights du second puzzle
                    result_2 = self.learning_system.extract_and_integrate_insights(analysis_data_2)
                    
                    self.assertTrue(result_1)
                    self.assertTrue(result_2)
                    
                    # Vérifier que les insights sont différents (pas de contamination)
                    # Ceci serait vérifié par le système de validation de généricité
    
    def test_learning_system_error_handling(self):
        """Test de la gestion d'erreurs du système d'apprentissage."""
        # Créer des données d'analyse au format attendu
        analysis_data = {
            "puzzle_id": "test_error",
            "analysis_data": {"raw_analysis": {"transformations": {"color": {"color_removal": True}}}}
        }
        
        # Test avec échec d'extraction d'insights
        with patch.object(self.learning_system, '_extract_raw_insights', side_effect=Exception("Extraction failed")):
            result = self.learning_system.extract_and_integrate_insights(analysis_data)
            self.assertFalse(result)
        
        # Test avec échec de validation
        with patch.object(self.learning_system, '_extract_raw_insights', return_value={"critical_observations": ["Valid insight"]}):
            with patch.object(self.learning_system, '_validate_insight_genericity', side_effect=Exception("Validation failed")):
                result = self.learning_system.extract_and_integrate_insights(analysis_data)
                self.assertFalse(result)
    
    def test_user_interaction_scenarios(self):
        """Test des différents scénarios d'interaction utilisateur."""
        # Test: Utilisateur accepte l'apprentissage
        with patch('builtins.input', return_value='y'):
            with patch.object(self.learning_system, 'extract_and_integrate_insights', return_value=True):
                result = self.learning_system.propose_learning(self.successful_analysis)
                self.assertTrue(result)
        
        # Test: Utilisateur refuse l'apprentissage
        with patch('builtins.input', return_value='n'):
            result = self.learning_system.propose_learning(self.successful_analysis)
            self.assertFalse(result)
        
        # Test: Réponse utilisateur invalide puis acceptation
        with patch('builtins.input', side_effect=['invalid', 'maybe', 'yes']):
            with patch.object(self.learning_system, 'extract_and_integrate_insights', return_value=True):
                result = self.learning_system.propose_learning(self.successful_analysis)
                self.assertTrue(result)
    
    def test_integration_with_automated_analyzer(self):
        """Test de l'intégration avec l'analyseur automatisé complet."""
        analyzer = AutomatedARCAnalyzer()
        
        # Tester que l'analyseur a bien un système d'apprentissage
        self.assertIsNotNone(analyzer.learning_system)
        self.assertIsInstance(analyzer.learning_system, LearningSystem)
        
        # Tester que l'option d'apprentissage est configurée
        self.assertTrue(analyzer.enable_learning)
        
        # Tester l'interaction avec le système d'apprentissage
        with patch.object(analyzer.learning_system, 'propose_learning') as mock_learning:
            mock_learning.return_value = True
            
            # Simuler une analyse réussie
            result = mock_learning(self.successful_analysis)
            self.assertTrue(result)
            mock_learning.assert_called_once_with(self.successful_analysis)
    
    def test_insight_persistence_and_reuse(self):
        """Test de la persistance et réutilisation des insights."""
        # Créer un fichier d'insights temporaire
        insights_file = self.temp_path / "analysis_data" / "extracted_insights.json"
        initial_insights = {
            "patterns": ["color_transformation", "spatial_alignment"],
            "rules": ["IF condition THEN action", "WHEN trigger THEN response"],
            "metadata": {"last_updated": datetime.now().isoformat(), "version": "1.0"}
        }
        
        with open(insights_file, 'w') as f:
            json.dump(initial_insights, f)
        
        # Test de l'intégration de nouveaux insights
        new_insights = ["geometric_transformation", "size_scaling"]
        
        # Créer des données d'analyse au format attendu
        analysis_data = {
            "puzzle_id": "test_persistence",
            "analysis_data": {"raw_analysis": {"transformations": {"geometric": {"rotation": True}}}}
        }
        
        with patch.object(self.learning_system, '_extract_raw_insights', return_value={"critical_observations": new_insights}):
            with patch.object(self.learning_system, '_validate_insight_genericity', return_value=True):
                # Ne pas mocker _integrate_insights pour permettre la vraie sauvegarde
                result = self.learning_system.extract_and_integrate_insights(analysis_data)
                
                self.assertTrue(result)
                
                # Vérifier que les insights ont été intégrés dans le cache
                self.assertGreater(len(self.learning_system.insights_cache), 0)
                
                # Vérifier qu'au moins un insight contient le terme recherché
                found_insight = False
                for insight in self.learning_system.insights_cache.values():
                    if "geometric_transformation" in insight.description:
                        found_insight = True
                        break
                
                self.assertTrue(found_insight, "L'insight 'geometric_transformation' devrait être trouvé dans le cache")
    
    def test_learning_system_performance(self):
        """Test des performances du système d'apprentissage."""
        import time
        
        # Test avec une analyse complexe
        complex_analysis = AnalysisResult(
            puzzle_id="complex_001",
            success=True,
            proposed_solution=np.random.randint(0, 10, (10, 10)),
            validation_result=ValidationResult(
                is_correct=True,
                accuracy_percentage=100.0,
                diagnostic_grid=np.full((10, 10), 'T'),
                total_errors=0,
                error_positions=[]
            ),
            ai_analysis=ParsedResponse(
                transformations=["complex_transform_" + str(i) for i in range(20)],
                patterns=["pattern_" + str(i) for i in range(15)],
                rules=["rule_" + str(i) for i in range(25)],
                proposed_grid=np.random.randint(0, 10, (10, 10)),
                reasoning_steps=["step_" + str(i) for i in range(50)],
                interpretation="Complex multi-step transformation",
                raw_response="Very long AI response with detailed analysis..."
            ),
            execution_time=5.0,
            saved_to=f"{self.temp_dir}/arc_results/puzzle_complex_001_success.txt",
            timestamp=datetime.now()
        )
        
        # Mesurer le temps d'exécution
        start_time = time.time()
        
        # Créer des données d'analyse au format attendu
        analysis_data = {
            "puzzle_id": "complex_001",
            "analysis_data": {"raw_analysis": {"transformations": {"color": {"color_removal": True}}}}
        }
        
        with patch.object(self.learning_system, '_extract_raw_insights', return_value={"critical_observations": ["insight1", "insight2"]}):
            with patch.object(self.learning_system, '_validate_insight_genericity', return_value=True):
                with patch.object(self.learning_system, '_integrate_insights', return_value=True):
                    result = self.learning_system.extract_and_integrate_insights(analysis_data)
        
        execution_time = time.time() - start_time
        
        self.assertTrue(result)
        self.assertLess(execution_time, 10.0, "Learning system should complete within 10 seconds")


class TestLearningSystemEdgeCases(unittest.TestCase):
    """Tests des cas limites pour le système d'apprentissage."""
    
    def setUp(self):
        """Configuration des tests."""
        self.learning_system = LearningSystem()
    
    def test_empty_analysis_handling(self):
        """Test de la gestion d'analyses vides ou incomplètes."""
        # Analyse avec données manquantes
        incomplete_analysis = AnalysisResult(
            puzzle_id="incomplete_001",
            success=False,  # Changé à False car validation_result est None
            proposed_solution=None,
            validation_result=None,
            ai_analysis=ParsedResponse(
                transformations=[],
                patterns=[],
                rules=[],
                proposed_grid=None,
                reasoning_steps=[],
                interpretation="",
                raw_response="Empty response"  # Ne peut pas être vide selon la validation
            ),
            execution_time=0.0,
            saved_to=None,
            timestamp=datetime.now()
        )
        
        # Créer des données d'analyse au format attendu
        analysis_data = {
            "puzzle_id": "incomplete_001",
            "analysis_data": {"raw_analysis": {}}
        }
        
        with patch.object(self.learning_system, '_extract_raw_insights', return_value=None):
            result = self.learning_system.extract_and_integrate_insights(analysis_data)
            # Le système devrait gérer gracieusement les analyses vides
            self.assertFalse(result)
    
    def test_malformed_insights_handling(self):
        """Test de la gestion d'insights malformés."""
        valid_analysis = AnalysisResult(
            puzzle_id="test_malformed",
            success=True,
            proposed_solution=np.array([[1, 2]]),
            validation_result=ValidationResult(
                is_correct=True,
                accuracy_percentage=100.0,
                diagnostic_grid=np.array([['T', 'T']]),
                total_errors=0,
                error_positions=[]
            ),
            ai_analysis=ParsedResponse(
                transformations=["valid_transform"],
                patterns=["valid_pattern"],
                rules=["valid_rule"],
                proposed_grid=np.array([[1, 2]]),
                reasoning_steps=["valid_step"],
                interpretation="valid interpretation",
                raw_response="valid response"
            ),
            execution_time=1.0,
            saved_to="/tmp/test.txt",
            timestamp=datetime.now()
        )
        
        # Insights malformés (None, types incorrects, etc.)
        malformed_insights = [None, "", 123, {"invalid": "type"}, []]
        
        # Créer des données d'analyse au format attendu
        analysis_data = {
            "puzzle_id": "test_malformed",
            "analysis_data": {"raw_analysis": {"transformations": {"color": {"color_removal": True}}}}
        }
        
        with patch.object(self.learning_system, '_extract_raw_insights', return_value={"critical_observations": malformed_insights}):
            with patch.object(self.learning_system, '_validate_insight_genericity', return_value=False):
                result = self.learning_system.extract_and_integrate_insights(analysis_data)
                self.assertFalse(result)
    
    def test_concurrent_learning_sessions(self):
        """Test de la gestion de sessions d'apprentissage concurrentes."""
        import threading
        import time
        
        results = []
        
        def learning_worker(puzzle_id):
            analysis = AnalysisResult(
                puzzle_id=puzzle_id,
                success=True,
                proposed_solution=np.array([[1, 2]]),
                validation_result=ValidationResult(
                    is_correct=True,
                    accuracy_percentage=100.0,
                    diagnostic_grid=np.array([['T', 'T']]),
                    total_errors=0,
                    error_positions=[]
                ),
                ai_analysis=ParsedResponse(
                    transformations=[f"transform_{puzzle_id}"],
                    patterns=[f"pattern_{puzzle_id}"],
                    rules=[f"rule_{puzzle_id}"],
                    proposed_grid=np.array([[1, 2]]),
                    reasoning_steps=[f"step_{puzzle_id}"],
                    interpretation=f"interpretation_{puzzle_id}",
                    raw_response=f"response_{puzzle_id}"
                ),
                execution_time=1.0,
                saved_to=f"/tmp/{puzzle_id}.txt",
                timestamp=datetime.now()
            )
            
            # Créer des données d'analyse au format attendu
            analysis_data = {
                "puzzle_id": puzzle_id,
                "analysis_data": {"raw_analysis": {"transformations": {"color": {"color_removal": True}}}}
            }
            
            with patch.object(self.learning_system, '_extract_raw_insights', return_value={"critical_observations": [f"insight_{puzzle_id}"]}):
                with patch.object(self.learning_system, '_validate_insight_genericity', return_value=True):
                    with patch.object(self.learning_system, '_integrate_insights', return_value=True):
                        result = self.learning_system.extract_and_integrate_insights(analysis_data)
                        results.append((puzzle_id, result))
        
        # Lancer plusieurs threads d'apprentissage
        threads = []
        for i in range(3):
            thread = threading.Thread(target=learning_worker, args=[f"concurrent_{i}"])
            threads.append(thread)
            thread.start()
        
        # Attendre la fin de tous les threads
        for thread in threads:
            thread.join()
        
        # Vérifier que tous les apprentissages ont réussi
        self.assertEqual(len(results), 3)
        for puzzle_id, result in results:
            self.assertTrue(result, f"Learning failed for {puzzle_id}")


if __name__ == '__main__':
    # Configuration du logging pour les tests
    import logging
    logging.basicConfig(level=logging.INFO)
    
    # Exécuter les tests
    unittest.main(verbosity=2)