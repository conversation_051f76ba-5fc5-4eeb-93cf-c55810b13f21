"""
Client Ollama pour l'intégration avec le système d'analyse ARC AGI.

Ce module fournit une interface pour communiquer avec <PERSON>,
permettant d'utiliser des modèles locaux pour l'analyse des puzzles.
"""

import json
import requests
import time
from typing import Dict, Any, Optional
from .constants import DefaultConfigurations


class OllamaError(Exception):
    """Exception levée lors d'erreurs avec Ollama."""
    pass


class OllamaClient:
    """Client pour communiquer avec Ollama."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialise le client Ollama.
        
        Args:
            config: Configuration Ollama (optionnel)
        """
        self.config = config or DefaultConfigurations.OLLAMA_CONFIG.copy()
        self.base_url = self.config.get('base_url', 'http://localhost:11434')
        self.model = self.config.get('model', 'llama3.1:8b')
        self.temperature = self.config.get('temperature', 0.1)
        self.max_tokens = self.config.get('max_tokens', 4000)
        self.timeout = self.config.get('timeout', 120)
    
    def is_available(self) -> bool:
        """
        Vérifie si Ollama est disponible.
        
        Returns:
            bool: True si Ollama répond, False sinon
        """
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except requests.RequestException:
            return False
    
    def list_models(self) -> list:
        """
        Liste les modèles disponibles dans Ollama.
        
        Returns:
            list: Liste des modèles disponibles
            
        Raises:
            OllamaError: Si impossible de récupérer la liste
        """
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=10)
            response.raise_for_status()
            data = response.json()
            return [model['name'] for model in data.get('models', [])]
        except requests.RequestException as e:
            raise OllamaError(f"Impossible de lister les modèles: {e}")
    
    def is_model_available(self, model_name: str) -> bool:
        """
        Vérifie si un modèle spécifique est disponible.
        
        Args:
            model_name: Nom du modèle à vérifier
            
        Returns:
            bool: True si le modèle est disponible
        """
        try:
            models = self.list_models()
            return model_name in models
        except OllamaError:
            return False
    
    def generate_response(self, prompt: str, model: Optional[str] = None) -> str:
        """
        Génère une réponse avec Ollama.
        
        Args:
            prompt: Le prompt à envoyer
            model: Modèle à utiliser (optionnel, utilise celui de la config)
            
        Returns:
            str: Réponse générée par le modèle
            
        Raises:
            OllamaError: Si erreur lors de la génération
        """
        model_to_use = model or self.model
        
        # Vérifier que le modèle est disponible
        if not self.is_model_available(model_to_use):
            available_models = self.list_models()
            raise OllamaError(
                f"Modèle '{model_to_use}' non disponible. "
                f"Modèles disponibles: {', '.join(available_models)}"
            )
        
        payload = {
            "model": model_to_use,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": self.temperature,
                "num_predict": self.max_tokens
            }
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            data = response.json()
            return data.get('response', '').strip()
            
        except requests.Timeout:
            raise OllamaError(f"Timeout après {self.timeout}s")
        except requests.RequestException as e:
            raise OllamaError(f"Erreur de requête: {e}")
        except json.JSONDecodeError as e:
            raise OllamaError(f"Réponse JSON invalide: {e}")
    
    def chat_completion(self, messages: list, model: Optional[str] = None) -> str:
        """
        Génère une réponse en mode chat.
        
        Args:
            messages: Liste des messages (format OpenAI-like)
            model: Modèle à utiliser (optionnel)
            
        Returns:
            str: Réponse du modèle
            
        Raises:
            OllamaError: Si erreur lors de la génération
        """
        model_to_use = model or self.model
        
        # Convertir les messages en prompt simple pour Ollama
        prompt_parts = []
        for msg in messages:
            role = msg.get('role', 'user')
            content = msg.get('content', '')
            
            if role == 'system':
                prompt_parts.append(f"System: {content}")
            elif role == 'user':
                prompt_parts.append(f"User: {content}")
            elif role == 'assistant':
                prompt_parts.append(f"Assistant: {content}")
        
        prompt = "\n\n".join(prompt_parts) + "\n\nAssistant:"
        
        return self.generate_response(prompt, model_to_use)
    
    def test_connection(self) -> Dict[str, Any]:
        """
        Teste la connexion à Ollama et retourne des informations.
        
        Returns:
            dict: Informations sur la connexion et les modèles
        """
        result = {
            'available': False,
            'models': [],
            'current_model': self.model,
            'error': None
        }
        
        try:
            if not self.is_available():
                result['error'] = "Ollama n'est pas accessible"
                return result
            
            result['available'] = True
            result['models'] = self.list_models()
            
            # Tester le modèle actuel
            if self.model not in result['models']:
                result['error'] = f"Modèle '{self.model}' non disponible"
            
        except Exception as e:
            result['error'] = str(e)
        
        return result


def create_ollama_client(model: str = None, base_url: str = None) -> OllamaClient:
    """
    Crée un client Ollama avec une configuration personnalisée.
    
    Args:
        model: Nom du modèle à utiliser
        base_url: URL de base d'Ollama
        
    Returns:
        OllamaClient: Instance configurée du client
    """
    config = DefaultConfigurations.OLLAMA_CONFIG.copy()
    
    if model:
        config['model'] = model
    if base_url:
        config['base_url'] = base_url
    
    return OllamaClient(config)