"""
Classes d'exception personnalisées pour le système d'analyse automatisée ARC AGI.

Ce module définit toutes les exceptions spécifiques au domaine utilisées
dans le workflow d'analyse automatisée.
"""


class ARCAnalysisError(Exception):
    """Exception de base pour toutes les erreurs d'analyse ARC."""
    pass


class PuzzleLoadingError(ARCAnalysisError):
    """Erreur lors du chargement d'un puzzle."""
    
    def __init__(self, puzzle_id: str, message: str):
        self.puzzle_id = puzzle_id
        super().__init__(f"Erreur chargement puzzle '{puzzle_id}': {message}")


class PuzzleNotFoundError(PuzzleLoadingError):
    """Puzzle non trouvé dans les données."""
    
    def __init__(self, puzzle_id: str, searched_paths: list = None):
        searched_paths = searched_paths or []
        paths_str = ", ".join(searched_paths) if searched_paths else "chemins standards"
        message = f"Puzzle non trouvé dans: {paths_str}"
        super().__init__(puzzle_id, message)


class InvalidPuzzleFormatError(PuzzleLoadingError):
    """Format de puzzle invalide."""
    
    def __init__(self, puzzle_id: str, format_issue: str):
        message = f"Format invalide: {format_issue}"
        super().__init__(puzzle_id, message)


class ChatSessionError(ARCAnalysisError):
    """Erreur lors de la communication avec la session chat."""
    
    def __init__(self, message: str, session_type: str = None):
        self.session_type = session_type
        if session_type:
            message = f"Erreur session '{session_type}': {message}"
        super().__init__(message)


class ChatConnectionError(ChatSessionError):
    """Erreur de connexion à la session chat."""
    pass


class ChatTimeoutError(ChatSessionError):
    """Timeout lors de la communication chat."""
    
    def __init__(self, timeout_seconds: float, session_type: str = None):
        message = f"Timeout après {timeout_seconds}s"
        super().__init__(message, session_type)


class ResponseParsingError(ARCAnalysisError):
    """Erreur lors du parsing de la réponse IA."""
    
    def __init__(self, message: str, raw_response: str = None):
        self.raw_response = raw_response
        super().__init__(f"Erreur parsing réponse: {message}")


class GridExtractionError(ResponseParsingError):
    """Erreur lors de l'extraction de grille depuis la réponse."""
    
    def __init__(self, message: str, raw_response: str = None):
        super().__init__(f"Extraction grille échouée: {message}", raw_response)


class SolutionValidationError(ARCAnalysisError):
    """Erreur lors de la validation de solution."""
    
    def __init__(self, message: str, proposed_shape: tuple = None, expected_shape: tuple = None):
        self.proposed_shape = proposed_shape
        self.expected_shape = expected_shape
        if proposed_shape and expected_shape:
            message = f"{message} (proposé: {proposed_shape}, attendu: {expected_shape})"
        super().__init__(message)


class DimensionMismatchError(SolutionValidationError):
    """Erreur de dimensions incompatibles entre grilles."""
    
    def __init__(self, proposed_shape: tuple, expected_shape: tuple):
        message = "Dimensions incompatibles"
        super().__init__(message, proposed_shape, expected_shape)


class LearningSystemError(ARCAnalysisError):
    """Erreur dans le système d'apprentissage."""
    pass


class InsightExtractionError(LearningSystemError):
    """Erreur lors de l'extraction d'insights."""
    
    def __init__(self, message: str, puzzle_id: str = None):
        self.puzzle_id = puzzle_id
        if puzzle_id:
            message = f"Extraction insights puzzle '{puzzle_id}': {message}"
        super().__init__(message)


class GenericityValidationError(LearningSystemError):
    """Erreur lors de la validation de généricité des insights."""
    
    def __init__(self, message: str, insight_description: str = None):
        self.insight_description = insight_description
        if insight_description:
            message = f"Validation généricité '{insight_description}': {message}"
        super().__init__(message)


class CrossContaminationError(GenericityValidationError):
    """Erreur de contamination croisée détectée."""
    
    def __init__(self, source_puzzle: str, target_puzzle: str, contaminated_element: str):
        self.source_puzzle = source_puzzle
        self.target_puzzle = target_puzzle
        self.contaminated_element = contaminated_element
        message = f"Contamination détectée de '{source_puzzle}' vers '{target_puzzle}': {contaminated_element}"
        super().__init__(message)


class FileOrganizationError(ARCAnalysisError):
    """Erreur de violation des règles d'organisation de fichiers."""
    
    def __init__(self, message: str, file_path: str = None):
        self.file_path = file_path
        if file_path:
            message = f"Organisation fichier '{file_path}': {message}"
        super().__init__(message)


class ConfigurationError(ARCAnalysisError):
    """Erreur de configuration du système."""
    
    def __init__(self, message: str, config_key: str = None):
        self.config_key = config_key
        if config_key:
            message = f"Configuration '{config_key}': {message}"
        super().__init__(message)