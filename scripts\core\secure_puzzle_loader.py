"""
Chargeur de puzzle sécurisé pour le système d'analyse automatisée ARC AGI.

Ce module fournit une classe pour charger les puzzles ARC en séparant
les données d'analyse des solutions cachées pour éviter la contamination.
"""

import json
import os
from pathlib import Path
from typing import Tu<PERSON>, Dict, Any, Optional
import numpy as np

from .data_models import PuzzleData
from .exceptions import (
    PuzzleNotFoundError,
    InvalidPuzzleFormatError,
    PuzzleLoadingError
)
from .constants import (
    PuzzleSubsets,
    FileExtensions,
    SystemLimits
)


class SecurePuzzleLoader:
    """
    Chargeur de puzzle sécurisé qui sépare les données d'analyse
    des solutions cachées pour éviter la contamination.
    """
    
    def __init__(self, arc_data_dir: str = "../arc-puzzle/arcdata"):
        """
        Initialise le chargeur avec le répertoire de données ARC.
        
        Args:
            arc_data_dir: Chemin vers le répertoire contenant les données ARC
        """
        self.arc_data_dir = Path(arc_data_dir)
        self._validate_data_directory()
    
    def _validate_data_directory(self) -> None:
        """Valide que le répertoire de données ARC existe et est correctement structuré."""
        if not self.arc_data_dir.exists():
            raise PuzzleLoadingError(
                "arc_data_dir",
                f"Répertoire de données ARC non trouvé: {self.arc_data_dir}"
            )
        
        # Vérifier que les sous-répertoires training et evaluation existent
        for subset in PuzzleSubsets.ALL:
            subset_dir = self.arc_data_dir / subset
            if not subset_dir.exists():
                raise PuzzleLoadingError(
                    subset,
                    f"Sous-répertoire {subset} non trouvé dans {self.arc_data_dir}"
                )
    
    def find_puzzle_file(self, puzzle_id: str) -> Tuple[Path, str]:
        """
        Localise le fichier puzzle dans les répertoires training ou evaluation.
        
        Args:
            puzzle_id: ID du puzzle à localiser
            
        Returns:
            Tuple contenant (chemin_fichier, subset)
            
        Raises:
            PuzzleNotFoundError: Si le puzzle n'est pas trouvé
        """
        searched_paths = []
        
        for subset in PuzzleSubsets.ALL:
            puzzle_file = self.arc_data_dir / subset / f"{puzzle_id}{FileExtensions.JSON}"
            searched_paths.append(str(puzzle_file))
            
            if puzzle_file.exists():
                return puzzle_file, subset
        
        raise PuzzleNotFoundError(puzzle_id, searched_paths)
    
    def _validate_puzzle_format(self, puzzle_data: Dict[str, Any], puzzle_id: str) -> None:
        """
        Valide le format du puzzle chargé.
        
        Args:
            puzzle_data: Données du puzzle à valider
            puzzle_id: ID du puzzle pour les messages d'erreur
            
        Raises:
            InvalidPuzzleFormatError: Si le format est invalide
        """
        # Vérifier la structure de base
        required_keys = ['train', 'test']
        for key in required_keys:
            if key not in puzzle_data:
                raise InvalidPuzzleFormatError(
                    puzzle_id,
                    f"Clé manquante: '{key}'"
                )
        
        # Vérifier que train est une liste non vide
        if not isinstance(puzzle_data['train'], list) or not puzzle_data['train']:
            raise InvalidPuzzleFormatError(
                puzzle_id,
                "La section 'train' doit être une liste non vide"
            )
        
        # Vérifier que test est une liste avec au moins un élément
        if not isinstance(puzzle_data['test'], list) or not puzzle_data['test']:
            raise InvalidPuzzleFormatError(
                puzzle_id,
                "La section 'test' doit être une liste non vide"
            )
        
        # Vérifier la structure des exemples d'entraînement
        for i, example in enumerate(puzzle_data['train']):
            if not isinstance(example, dict):
                raise InvalidPuzzleFormatError(
                    puzzle_id,
                    f"Exemple d'entraînement {i} doit être un dictionnaire"
                )
            
            if 'input' not in example or 'output' not in example:
                raise InvalidPuzzleFormatError(
                    puzzle_id,
                    f"Exemple d'entraînement {i} manque 'input' ou 'output'"
                )
            
            # Vérifier les dimensions des grilles
            self._validate_grid(example['input'], puzzle_id, f"train[{i}].input")
            self._validate_grid(example['output'], puzzle_id, f"train[{i}].output")
        
        # Vérifier la structure du test
        test_example = puzzle_data['test'][0]
        if not isinstance(test_example, dict):
            raise InvalidPuzzleFormatError(
                puzzle_id,
                "L'exemple de test doit être un dictionnaire"
            )
        
        if 'input' not in test_example:
            raise InvalidPuzzleFormatError(
                puzzle_id,
                "L'exemple de test manque 'input'"
            )
        
        self._validate_grid(test_example['input'], puzzle_id, "test[0].input")
        
        # Si output existe dans test, le valider aussi
        if 'output' in test_example:
            self._validate_grid(test_example['output'], puzzle_id, "test[0].output")
    
    def _validate_grid(self, grid: Any, puzzle_id: str, context: str) -> None:
        """
        Valide qu'une grille a le bon format.
        
        Args:
            grid: Grille à valider
            puzzle_id: ID du puzzle pour les messages d'erreur
            context: Contexte de la grille pour les messages d'erreur
        """
        if not isinstance(grid, list):
            raise InvalidPuzzleFormatError(
                puzzle_id,
                f"{context}: La grille doit être une liste"
            )
        
        if not grid:
            raise InvalidPuzzleFormatError(
                puzzle_id,
                f"{context}: La grille ne peut pas être vide"
            )
        
        # Vérifier que toutes les lignes ont la même longueur
        row_length = len(grid[0])
        for i, row in enumerate(grid):
            if not isinstance(row, list):
                raise InvalidPuzzleFormatError(
                    puzzle_id,
                    f"{context}: Ligne {i} doit être une liste"
                )
            
            if len(row) != row_length:
                raise InvalidPuzzleFormatError(
                    puzzle_id,
                    f"{context}: Ligne {i} a une longueur différente ({len(row)} vs {row_length})"
                )
            
            # Vérifier que tous les éléments sont des entiers valides (couleurs ARC)
            for j, cell in enumerate(row):
                if not isinstance(cell, int) or not (0 <= cell <= 9):
                    raise InvalidPuzzleFormatError(
                        puzzle_id,
                        f"{context}: Cellule [{i}][{j}] doit être un entier entre 0 et 9"
                    )
        
        # Vérifier les limites de taille
        height, width = len(grid), len(grid[0])
        if not (SystemLimits.MIN_GRID_SIZE <= height <= SystemLimits.MAX_GRID_SIZE):
            raise InvalidPuzzleFormatError(
                puzzle_id,
                f"{context}: Hauteur invalide {height} (doit être entre {SystemLimits.MIN_GRID_SIZE} et {SystemLimits.MAX_GRID_SIZE})"
            )
        
        if not (SystemLimits.MIN_GRID_SIZE <= width <= SystemLimits.MAX_GRID_SIZE):
            raise InvalidPuzzleFormatError(
                puzzle_id,
                f"{context}: Largeur invalide {width} (doit être entre {SystemLimits.MIN_GRID_SIZE} et {SystemLimits.MAX_GRID_SIZE})"
            )
    
    def load_puzzle_for_analysis(self, puzzle_id: str) -> Tuple[PuzzleData, Optional[np.ndarray]]:
        """
        Charge un puzzle en séparant les données d'analyse de la solution cachée.
        
        Args:
            puzzle_id: ID du puzzle à charger
            
        Returns:
            Tuple contenant:
            - PuzzleData: Données du puzzle pour l'analyse (sans solution test)
            - Optional[np.ndarray]: Solution cachée (None si pas disponible)
            
        Raises:
            PuzzleNotFoundError: Si le puzzle n'est pas trouvé
            InvalidPuzzleFormatError: Si le format du puzzle est invalide
            PuzzleLoadingError: Pour d'autres erreurs de chargement
        """
        try:
            # Localiser le fichier puzzle
            puzzle_file, subset = self.find_puzzle_file(puzzle_id)
            
            # Charger les données JSON
            with open(puzzle_file, 'r', encoding='utf-8') as f:
                puzzle_data = json.load(f)
            
            # Valider le format
            self._validate_puzzle_format(puzzle_data, puzzle_id)
            
            # Extraire les données d'entraînement
            train_examples = []
            for example in puzzle_data['train']:
                train_examples.append({
                    'input': example['input'],
                    'output': example['output']
                })
            
            # Extraire l'input de test
            test_input = np.array(puzzle_data['test'][0]['input'])
            
            # Extraire la solution cachée si disponible
            hidden_solution = None
            if 'output' in puzzle_data['test'][0]:
                hidden_solution = np.array(puzzle_data['test'][0]['output'])
            
            # Créer l'objet PuzzleData (sans la solution cachée)
            puzzle_data_obj = PuzzleData(
                puzzle_id=puzzle_id,
                train_examples=train_examples,
                test_input=test_input,
                hidden_solution=None,  # Toujours None pour éviter la contamination
                subset=subset
            )
            
            return puzzle_data_obj, hidden_solution
            
        except (OSError, IOError) as e:
            raise PuzzleLoadingError(
                puzzle_id,
                f"Erreur de lecture du fichier: {str(e)}"
            )
        except json.JSONDecodeError as e:
            raise InvalidPuzzleFormatError(
                puzzle_id,
                f"Erreur de parsing JSON: {str(e)}"
            )
        except (InvalidPuzzleFormatError, PuzzleNotFoundError):
            # Re-raise ces exceptions spécifiques sans les wrapper
            raise
        except Exception as e:
            raise PuzzleLoadingError(
                puzzle_id,
                f"Erreur inattendue: {str(e)}"
            )
    
    def get_available_puzzles(self, subset: Optional[str] = None) -> Dict[str, list]:
        """
        Retourne la liste des puzzles disponibles.
        
        Args:
            subset: Sous-ensemble spécifique ('training' ou 'evaluation'), 
                   ou None pour tous
                   
        Returns:
            Dictionnaire avec les sous-ensembles comme clés et listes d'IDs comme valeurs
        """
        available = {}
        
        subsets_to_check = [subset] if subset else PuzzleSubsets.ALL
        
        for subset_name in subsets_to_check:
            if subset_name not in PuzzleSubsets.ALL:
                continue
                
            subset_dir = self.arc_data_dir / subset_name
            if not subset_dir.exists():
                available[subset_name] = []
                continue
            
            puzzle_ids = []
            for file_path in subset_dir.glob(f"*{FileExtensions.JSON}"):
                puzzle_id = file_path.stem
                puzzle_ids.append(puzzle_id)
            
            available[subset_name] = sorted(puzzle_ids)
        
        return available