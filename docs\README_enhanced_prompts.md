# Générateur de Prompts ARC AGI Amélioré

Ce système génère des prompts optimisés pour la résolution de puzzles ARC AGI en intégrant automatiquement des insights critiques extraits de l'analyse des données.

## 🎯 Problème Résolu

Lors de la résolution manuelle de puzzles ARC AGI, il est facile de passer à côté d'informations critiques qui auraient permis de trouver la solution plus rapidement. Ce système identifie automatiquement ces informations **basées uniquement sur l'analyse automatique de chaque puzzle** et les présente de manière claire dans le prompt.

## ⚠️ Principe de Généricité

**IMPORTANT**: Le système extrait uniquement des informations génériques basées sur l'analyse automatique de chaque puzzle individuel. Aucune information hardcodée ou spécifique à un puzzle particulier n'est utilisée, évitant ainsi toute contamination croisée entre puzzles.

## 📁 Fichiers Principaux

- `extract_key_insights.py` - Extrait les insights critiques du fichier d'analyse JSON
- `arc_prompt_generator4.py` - Générateur de prompts modifié pour intégrer les insights
- `generate_enhanced_prompt.py` - Script principal pour une utilisation simplifiée
- `test_enhanced_prompt.py` - Script de test

## 🚀 Utilisation

### Méthode Simple (Recommandée)

```bash
# Générer un prompt amélioré
python generate_enhanced_prompt.py --taskid 2204b7a8

# Pour un puzzle d'évaluation
python generate_enhanced_prompt.py --taskid 2204b7a8 --subset evaluation

# Voir seulement les insights sans générer le prompt complet
python generate_enhanced_prompt.py --taskid 2204b7a8 --show-insights-only
```

### Méthode Détaillée

```bash
# 1. Extraire les insights d'un fichier d'analyse
python extract_key_insights.py --analysis-file "analysis_data/training/2204b7a8_analysis.json"

# 2. Générer le prompt avec insights intégrés
python arc_prompt_generator4.py --taskid 2204b7a8 --subset training --arc-data-dir "../arc-puzzle/arcdata/"
```

## 🔍 Insights Automatiquement Détectés

Le système identifie automatiquement :

### 1. **Couleurs Cibles**
- Quelles couleurs sont transformées
- Fréquence des transformations
- Patterns de changement

### 2. **Structure des Objets**
- Objets de bordure vs points isolés
- Tailles et distributions
- Consistance entre exemples

### 3. **Patterns Spatiaux**
- Types de bordures (horizontales/verticales)
- Alignements et orientations
- Relations spatiales

### 4. **Type de Transformation**
- Géométrique vs couleur
- Sur place vs déplacement
- Mécanismes détectés

### 5. **Statistiques de Changement**
- Pourcentage de pixels modifiés
- Impact local vs global
- Zones de transformation

### 6. **Règles Probables**
- Proximité/distance
- Gravité/glissement
- Patterns de remplacement

## 📊 Exemple de Sortie

```
🔍 INSIGHTS CRITIQUES (basés sur l'analyse automatique):
• COULEUR CIBLE: Seule la couleur 3 est transformée dans tous les exemples
• ALIGNEMENT: Patterns détectés - mixed, horizontal
• TRANSFORMATION: Suppression de couleur détectée (pas de déplacement géométrique)
• PATTERN: Transformations de couleur avec objets de tailles variées - possibles règles spatiales

• CHANGEMENTS DE COULEUR:
  - 3→4: 3 occurrences
  - 3→8: 3 occurrences
  - 3→9: 3 occurrences
• TAILLES D'OBJETS: taille 1 (14x), taille 10 (6x)

💡 APPROCHE RECOMMANDÉE:
• Se concentrer sur les transformations de couleur plutôt que les déplacements
• Chercher des transformations locales et ciblées
• Tester systématiquement l'hypothèse sur tous les exemples
```

## 🛠️ Prérequis

1. Fichier d'analyse JSON généré par `generate_analysis.py`
2. Données ARC AGI dans le format standard
3. Python 3.6+

## 📂 Structure des Fichiers

```
arc-solver/
├── extract_key_insights.py          # Extracteur d'insights
├── arc_prompt_generator4.py         # Générateur modifié
├── generate_enhanced_prompt.py      # Script principal
├── test_enhanced_prompt.py          # Tests
├── analysis_data/                   # Analyses JSON
│   └── training/
│       └── 2204b7a8_analysis.json
├── arc_results/                     # Prompts générés
│   └── training/
│       └── 2204b7a8_prompt.txt
└── ../arc-puzzle/arcdata/           # Données ARC
    └── training/
        └── 2204b7a8.json
```

## 🧪 Tests et Validation

```bash
# Tester le système complet
python test_enhanced_prompt.py

# Valider que les insights sont génériques (pas de contamination croisée)
python validate_generic_insights.py

# Vérifier qu'un puzzle spécifique fonctionne
python generate_enhanced_prompt.py --taskid 2204b7a8 --show-insights-only
```

### Validation de Généricité

Le script `validate_generic_insights.py` vérifie que :
- Aucun mot-clé hardcodé n'est utilisé
- Les insights sont différents entre puzzles (pas de contamination)
- Toutes les sections requises sont présentes

## 💡 Avantages

1. **Gain de Temps** - Identifie immédiatement les informations critiques
2. **Évite les Fausses Pistes** - Signale les transformations réelles vs apparentes
3. **Approche Systématique** - Structure l'analyse de manière méthodique
4. **Insights Quantifiés** - Fournit des statistiques précises
5. **Règles Probables** - Suggère des hypothèses de départ

## 🔧 Personnalisation

Pour ajouter de nouveaux types d'insights, modifiez la fonction `extract_key_insights()` dans `extract_key_insights.py`.

## 📈 Résultats

Sur le puzzle 2204b7a8, le système identifie correctement :
- Que seule la couleur verte (3) est transformée
- Que c'est une transformation de couleur, pas un déplacement
- Que la règle est basée sur la proximité aux bordures
- Que seulement 4.7% des pixels sont modifiés

Ces informations auraient permis de résoudre le puzzle beaucoup plus rapidement !