"""
Système de session chat pour l'analyse automatisée ARC AGI.

Ce module gère les interactions avec les API de chat IA pour l'analyse
des puzzles ARC AGI, incluant la gestion des sessions, historique et
différents types de sessions.
"""

import time
import json
from typing import Optional, List, Dict, Any
from datetime import datetime
from dataclasses import dataclass, field

from .constants import (
    ChatSessionTypes, 
    SystemLimits, 
    DefaultConfigurations,
    ExitCodes,
    AIProviders
)
from .ollama_client import OllamaClient, OllamaError
from .openai_client import OpenAIClient, OpenAIError
from .groq_client import GroqClient, GroqError
from .huggingface_client import HuggingFaceClient, HuggingFaceError


@dataclass
class ChatMessage:
    """Message dans une conversation chat."""
    role: str  # 'user' ou 'assistant'
    content: str
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convertit le message en dictionnaire."""
        return {
            'role': self.role,
            'content': self.content,
            'timestamp': self.timestamp.isoformat()
        }


@dataclass
class SessionConfig:
    """Configuration d'une session chat."""
    session_type: str = ChatSessionTypes.DEFAULT
    timeout: int = SystemLimits.CHAT_TIMEOUT_SECONDS
    max_retries: int = SystemLimits.MAX_RETRY_ATTEMPTS
    enable_history: bool = True
    max_history_length: int = 50
    ai_provider: str = AIProviders.SIMULATION
    ollama_model: Optional[str] = None
    ollama_base_url: Optional[str] = None
    openai_model: Optional[str] = None
    groq_model: Optional[str] = None
    openrouter_model: Optional[str] = None
    huggingface_model: Optional[str] = None


class ChatSessionError(Exception):
    """Exception pour les erreurs de session chat."""
    pass


class ChatSession:
    """
    Gestionnaire de session chat pour l'analyse ARC AGI.
    
    Cette classe gère les interactions avec les API de chat IA,
    maintient l'historique des conversations et supporte différents
    types de sessions selon les besoins d'analyse.
    """
    
    def __init__(self, config: Optional[SessionConfig] = None):
        """
        Initialise une nouvelle session chat.
        
        Args:
            config: Configuration de la session. Si None, utilise la config par défaut.
        """
        self.config = config or SessionConfig()
        self.session_id = self._generate_session_id()
        self.conversation_history: List[ChatMessage] = []
        self.is_active = False
        self.created_at = datetime.now()
        self.last_activity = self.created_at
        
        # Initialiser les clients IA selon le provider
        self.ollama_client = None
        self.openai_client = None
        self.openrouter_client = None
        self.huggingface_client = None
        
        if self.config.ai_provider == AIProviders.OLLAMA:
            self._init_ollama_client()
        elif self.config.ai_provider == AIProviders.GROQ:
            self._init_groq_client()
        elif self.config.ai_provider == AIProviders.OPENAI:
            self._init_openai_client()
        elif self.config.ai_provider == AIProviders.OPENROUTER:
            self._init_openrouter_client()
        elif self.config.ai_provider == AIProviders.HUGGINGFACE:
            self._init_huggingface_client()
        
        # Validation de la configuration
        self._validate_config()
    
    def _generate_session_id(self) -> str:
        """Génère un ID unique pour la session."""
        import random
        timestamp = int(time.time() * 1000000)  # Microseconds for better uniqueness
        random_suffix = random.randint(1000, 9999)
        return f"chat_session_{timestamp}_{random_suffix}"
    
    def _init_ollama_client(self) -> None:
        """Initialise le client Ollama."""
        try:
            config = {}
            if self.config.ollama_model:
                config['model'] = self.config.ollama_model
            if self.config.ollama_base_url:
                config['base_url'] = self.config.ollama_base_url
            
            self.ollama_client = OllamaClient(config if config else None)
            
            # Tester la connexion
            test_result = self.ollama_client.test_connection()
            if not test_result['available']:
                raise ChatSessionError(f"Ollama non disponible: {test_result.get('error', 'Raison inconnue')}")
                
        except Exception as e:
            raise ChatSessionError(f"Impossible d'initialiser Ollama: {e}")
    
    def _init_openai_client(self) -> None:
        """Initialise le client OpenAI."""
        try:
            model = self.config.openai_model
            self.openai_client = OpenAIClient('openai', {'model': model} if model else None)
            
            # Tester la connexion
            test_result = self.openai_client.test_connection()
            if not test_result['available']:
                raise ChatSessionError(f"OpenAI non disponible: {test_result.get('error', 'Raison inconnue')}")
                
        except Exception as e:
            raise ChatSessionError(f"Impossible d'initialiser OpenAI: {e}")

    def _init_groq_client(self) -> None:
        """Initialise le client Groq."""
        try:
            model = self.config.groq_model
            self.groq_client = GroqClient('groq', {'model': model} if model else None)
            
            # Tester la connexion
            test_result = self.groq_client.test_connection()
            if not test_result['available']:
                raise ChatSessionError(f"Groq non disponible: {test_result.get('error', 'Raison inconnue')}")

        except Exception as e:
            raise ChatSessionError(f"Impossible d'initialiser Groq: {e}")

    def _init_openrouter_client(self) -> None:
        """Initialise le client OpenRouter."""
        try:
            model = self.config.openrouter_model
            self.openrouter_client = OpenAIClient('openrouter', {'model': model} if model else None)
            
            # Tester la connexion
            test_result = self.openrouter_client.test_connection()
            if not test_result['available']:
                raise ChatSessionError(f"OpenRouter non disponible: {test_result.get('error', 'Raison inconnue')}")
                
        except Exception as e:
            raise ChatSessionError(f"Impossible d'initialiser OpenRouter: {e}")
    
    def _init_huggingface_client(self) -> None:
        """Initialise le client Hugging Face."""
        try:
            model = self.config.huggingface_model
            self.huggingface_client = HuggingFaceClient({'model': model} if model else None)
            
            # Tester la connexion
            test_result = self.huggingface_client.test_connection()
            if not test_result['available']:
                raise ChatSessionError(f"Hugging Face non disponible: {test_result.get('error', 'Raison inconnue')}")
                
        except Exception as e:
            raise ChatSessionError(f"Impossible d'initialiser Hugging Face: {e}")
    
    def _validate_config(self) -> None:
        """Valide la configuration de la session."""
        valid_types = [
            ChatSessionTypes.DEFAULT,
            ChatSessionTypes.BLIND_ANALYSIS,
            ChatSessionTypes.LEARNING_EXTRACTION,
            ChatSessionTypes.VALIDATION_REVIEW
        ]
        
        valid_providers = [
            AIProviders.SIMULATION,
            AIProviders.OLLAMA,
            AIProviders.OPENAI,
            AIProviders.GROQ,
            AIProviders.OPENROUTER,
            AIProviders.HUGGINGFACE,
            AIProviders.CLAUDE
        ]
        
        if self.config.session_type not in valid_types:
            raise ChatSessionError(f"Type de session invalide: {self.config.session_type}")
        
        if self.config.ai_provider not in valid_providers:
            raise ChatSessionError(f"Fournisseur IA invalide: {self.config.ai_provider}")
        
        if self.config.timeout <= 0:
            raise ChatSessionError("Le timeout doit être positif")
        
        if self.config.max_retries < 0:
            raise ChatSessionError("max_retries ne peut pas être négatif")
    
    def create_new_session(self) -> bool:
        """
        Crée une nouvelle session "à l'aveugle" pour l'analyse.
        
        Cette méthode initialise une session fraîche sans historique
        pour garantir une analyse non biaisée du puzzle.
        
        Returns:
            bool: True si la session a été créée avec succès
            
        Raises:
            ChatSessionError: Si la création de session échoue
        """
        try:
            # Réinitialiser l'état de la session
            self.conversation_history.clear()
            self.session_id = self._generate_session_id()
            self.created_at = datetime.now()
            self.last_activity = self.created_at
            self.is_active = True
            
            # Configuration spéciale pour l'analyse à l'aveugle
            if self.config.session_type == ChatSessionTypes.BLIND_ANALYSIS:
                # Désactiver l'historique pour éviter la contamination
                self.config.enable_history = False
            
            return True
            
        except Exception as e:
            raise ChatSessionError(f"Échec de création de session: {str(e)}")
    
    def send_prompt(self, prompt: str) -> str:
        """
        Envoie un prompt et récupère la réponse complète.
        
        Cette méthode gère l'envoi du prompt à l'API de chat,
        la gestion des erreurs, les tentatives multiples et
        la mise à jour de l'historique.
        
        Args:
            prompt: Le prompt à envoyer à l'IA
            
        Returns:
            str: La réponse complète de l'IA
            
        Raises:
            ChatSessionError: Si l'envoi échoue après toutes les tentatives
        """
        if not prompt.strip():
            raise ChatSessionError("Le prompt ne peut pas être vide")
        
        if not self.is_active:
            raise ChatSessionError("Session non active. Créer une nouvelle session d'abord.")
        
        # Sauvegarder le prompt avant envoi
        self._save_prompt(prompt)
        
        # Ajouter le message utilisateur à l'historique
        user_message = ChatMessage(role="user", content=prompt)
        if self.config.enable_history:
            self._add_to_history(user_message)
        
        # Tentatives d'envoi avec retry
        last_error = None
        for attempt in range(self.config.max_retries + 1):
            try:
                if self.config.ai_provider == AIProviders.OPENROUTER and hasattr(self, 'openrouter_client'):
                    model = getattr(self.openrouter_client, 'model', 'inconnu')
                    if 'deepseek-r1' in model:
                        print(f"🧠 Tentative {attempt + 1}/{self.config.max_retries + 1} - DeepSeek R1 réfléchit... (peut prendre 5-10 min)")
                    else:
                        print(f"🔄 Tentative {attempt + 1}/{self.config.max_retries + 1} - Envoi à {self.config.ai_provider}...")
                else:
                    print(f"🔄 Tentative {attempt + 1}/{self.config.max_retries + 1} - Envoi à {self.config.ai_provider}...")
                
                response = self._send_to_api(prompt, attempt)
                print(f"✅ Réponse reçue ({len(response)} caractères)")
                
                # Ajouter la réponse à l'historique
                assistant_message = ChatMessage(role="assistant", content=response)
                if self.config.enable_history:
                    self._add_to_history(assistant_message)
                
                self.last_activity = datetime.now()
                return response
                
            except Exception as e:
                last_error = e
                if attempt < self.config.max_retries:
                    # Attendre avant la prochaine tentative (backoff exponentiel)
                    wait_time = 2 ** attempt
                    time.sleep(wait_time)
                    continue
                else:
                    break
        
        # Toutes les tentatives ont échoué
        error_msg = f"Échec d'envoi après {self.config.max_retries + 1} tentatives: {str(last_error)}"
        raise ChatSessionError(error_msg)
    
    def _save_prompt(self, prompt: str) -> None:
        """Sauvegarde le prompt avant envoi pour debug/étude."""
        try:
            from pathlib import Path
            from datetime import datetime
            
            # Créer le dossier de sauvegarde
            save_dir = Path("arc_results/prompts")
            save_dir.mkdir(parents=True, exist_ok=True)
            
            # Nom de fichier avec timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            provider = getattr(self.config, 'ai_provider', 'unknown')
            filename = f"{timestamp}_{provider}_prompt.txt"
            
            # Sauvegarder
            with open(save_dir / filename, 'w', encoding='utf-8') as f:
                f.write(f"# Prompt envoyé à l'IA\n")
                f.write(f"# Provider: {provider}\n")
                f.write(f"# Session type: {self.config.session_type}\n")
                f.write(f"# Timestamp: {datetime.now().isoformat()}\n")
                f.write(f"# Taille: {len(prompt)} caractères\n")
                f.write(f"\n{'='*80}\n\n")
                f.write(prompt)
            
            print(f"📝 Prompt sauvegardé: {save_dir / filename}")
            
        except Exception as e:
            print(f"⚠️ Erreur sauvegarde prompt: {e}")
    
    def _send_to_api(self, prompt: str, attempt: int) -> str:
        """
        Envoie le prompt à l'API de chat.
        
        Args:
            prompt: Le prompt à envoyer
            attempt: Numéro de la tentative (pour logging)
            
        Returns:
            str: Réponse de l'API
            
        Raises:
            Exception: Si l'API retourne une erreur
        """
        if self.config.ai_provider == AIProviders.OLLAMA:
            return self._send_to_ollama(prompt, attempt)
        elif self.config.ai_provider == AIProviders.OPENAI:
            return self._send_to_openai(prompt, attempt)
        elif self.config.ai_provider == AIProviders.GROQ:
            return self._send_to_groq(prompt, attempt)
        elif self.config.ai_provider == AIProviders.OPENROUTER:
            return self._send_to_openrouter(prompt, attempt)
        elif self.config.ai_provider == AIProviders.HUGGINGFACE:
            return self._send_to_huggingface(prompt, attempt)
        elif self.config.ai_provider == AIProviders.SIMULATION:
            return self._send_to_simulation(prompt, attempt)
        else:
            raise ChatSessionError(f"Fournisseur IA non implémenté: {self.config.ai_provider}")
    
    def _send_to_ollama(self, prompt: str, attempt: int) -> str:
        """
        Envoie le prompt à Ollama.
        
        Args:
            prompt: Le prompt à envoyer
            attempt: Numéro de la tentative
            
        Returns:
            str: Réponse d'Ollama
            
        Raises:
            Exception: Si Ollama retourne une erreur
        """
        if not self.ollama_client:
            raise ChatSessionError("Client Ollama non initialisé")
        
        try:
            # Créer un prompt système selon le type de session
            system_prompt = self._get_system_prompt_for_session()
            
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})
            
            response = self.ollama_client.chat_completion(messages)
            return response
            
        except OllamaError as e:
            raise Exception(f"Erreur Ollama: {e}")
        except Exception as e:
            raise Exception(f"Erreur inattendue avec Ollama: {e}")
    
    def _send_to_openai(self, prompt: str, attempt: int) -> str:
        """
        Envoie le prompt à OpenAI.
        
        Args:
            prompt: Le prompt à envoyer
            attempt: Numéro de la tentative
            
        Returns:
            str: Réponse d'OpenAI
        """
        if not self.openai_client:
            raise ChatSessionError("Client OpenAI non initialisé")
        
        try:
            system_prompt = self._get_system_prompt_for_session()
            
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})
            
            response = self.openai_client.chat_completion(messages)
            return response
            
        except OpenAIError as e:
            raise Exception(f"Erreur OpenAI: {e}")
        except Exception as e:
            raise Exception(f"Erreur inattendue avec OpenAI: {e}")
    
    def _send_to_groq(self, prompt: str, attempt: int) -> str:
        """
        Envoie le prompt à Groq.
        
        Args:
            prompt: Le prompt à envoyer
            attempt: Numéro de la tentative
            
        Returns:
            str: Réponse de Groq
        """
        if not self.groq_client:
            raise ChatSessionError("Client Groq non initialisé")
        
        try:
            system_prompt = self._get_system_prompt_for_session()
            
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})
            
            response = self.groq_client.chat_completion(messages, retry_on_truncation=True)
            return response
            
        except GroqError as e:
            raise Exception(f"Erreur Groq: {e}")
        except Exception as e:
            raise Exception(f"Erreur inattendue avec Groq: {e}")

    def _send_to_openrouter(self, prompt: str, attempt: int) -> str:
        """
        Envoie le prompt à OpenRouter.
        
        Args:
            prompt: Le prompt à envoyer
            attempt: Numéro de la tentative
            
        Returns:
            str: Réponse d'OpenRouter
        """
        if not self.openrouter_client:
            raise ChatSessionError("Client OpenRouter non initialisé")
        
        try:
            system_prompt = self._get_system_prompt_for_session()
            
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})
            
            response = self.openrouter_client.chat_completion(messages, retry_on_truncation=True)
            return response
            
        except OpenAIError as e:
            raise Exception(f"Erreur OpenRouter: {e}")
        except Exception as e:
            raise Exception(f"Erreur inattendue avec OpenRouter: {e}")
    
    def _send_to_huggingface(self, prompt: str, attempt: int) -> str:
        """
        Envoie le prompt à Hugging Face.
        
        Args:
            prompt: Le prompt à envoyer
            attempt: Numéro de la tentative
            
        Returns:
            str: Réponse de Hugging Face
        """
        if not self.huggingface_client:
            raise ChatSessionError("Client Hugging Face non initialisé")
        
        try:
            system_prompt = self._get_system_prompt_for_session()
            
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})
            
            response = self.huggingface_client.chat_completion(messages)
            return response
            
        except HuggingFaceError as e:
            raise Exception(f"Erreur Hugging Face: {e}")
        except Exception as e:
            raise Exception(f"Erreur inattendue avec Hugging Face: {e}")
    
    def _send_to_simulation(self, prompt: str, attempt: int) -> str:
        """
        Envoie le prompt à la simulation (ancien comportement).
        
        Args:
            prompt: Le prompt à envoyer
            attempt: Numéro de la tentative
            
        Returns:
            str: Réponse simulée
        """
        # Simuler un délai d'API
        time.sleep(0.1)
        
        # Simuler des erreurs occasionnelles pour tester le retry
        if attempt == 0 and "test_error" in prompt.lower():
            raise Exception("Erreur simulée d'API")
        
        # Réponse simulée basée sur le type de session
        if self.config.session_type == ChatSessionTypes.BLIND_ANALYSIS:
            return self._generate_analysis_response(prompt)
        elif self.config.session_type == ChatSessionTypes.LEARNING_EXTRACTION:
            return self._generate_learning_response(prompt)
        elif self.config.session_type == ChatSessionTypes.VALIDATION_REVIEW:
            return self._generate_validation_response(prompt)
        else:
            return self._generate_default_response(prompt)
    
    def _get_system_prompt_for_session(self) -> str:
        """
        Retourne le prompt système selon le type de session.
        
        Returns:
            str: Prompt système approprié
        """
        if self.config.session_type == ChatSessionTypes.BLIND_ANALYSIS:
            return """Tu es un expert français en résolution de puzzles ARC AGI. 

🇫🇷 LANGUE OBLIGATOIRE : FRANÇAIS UNIQUEMENT 🇫🇷

INSTRUCTIONS CRITIQUES:
- Tu DOIS répondre EXCLUSIVEMENT en français - AUCUN mot anglais autorisé
- Si tu commences à écrire en anglais, ARRÊTE-TOI et recommence en français
- Analyse le puzzle fourni et propose une solution complète
- Sois TRÈS détaillé et exhaustif dans ton raisonnement (minimum 5000 mots)
- Prends le temps de bien analyser chaque exemple individuellement
- Explique chaque étape de ton processus de réflexion en français
- Documente toutes tes observations, même les plus petites, en français

MÉTHODOLOGIE:
1. Analyse attentive des exemples d'entrée/sortie pour identifier les patterns
2. Identification des transformations appliquées (couleurs, formes, positions)
3. Déduction des règles générales qui gouvernent la transformation
4. Application rigoureuse de ces règles à l'entrée de test
5. Vérification de la cohérence de ta solution

FORMAT DE RÉPONSE OBLIGATOIRE:
TRANSFORMATIONS DÉTECTÉES:
- [Liste détaillée des transformations identifiées]

PATTERNS IDENTIFIÉS:
- [Liste des patterns et structures trouvés]

RÈGLES DÉDUITES:
- [Liste des règles générales déduites]

RAISONNEMENT DÉTAILLÉ:
[Explique étape par étape comment tu appliques les règles]

GRILLE OUTPUT PROPOSÉE:
[[grille de sortie au format JSON - OBLIGATOIRE]]

EXEMPLE DE FORMAT DE GRILLE:
[[0,1,2],[3,4,5],[6,7,8]]

IMPORTANT: Tu DOIS absolument fournir la grille de sortie finale au format JSON avec des crochets doubles [[...]] sinon ta réponse sera considérée comme incomplète.

Sois précis, méthodique et détaillé dans ton analyse en français."""

        elif self.config.session_type == ChatSessionTypes.LEARNING_EXTRACTION:
            return """Tu es un expert en extraction d'insights pour l'apprentissage automatique sur les puzzles ARC AGI.

Ton rôle est d'analyser les puzzles résolus et d'extraire des insights génériques qui peuvent aider à résoudre d'autres puzzles similaires.

IMPORTANT: Les insights doivent être génériques et ne pas contenir d'informations spécifiques à un puzzle particulier."""

        elif self.config.session_type == ChatSessionTypes.VALIDATION_REVIEW:
            return """Tu es un expert en validation de solutions ARC AGI.

Analyse la solution proposée et compare-la avec la solution attendue. Identifie les erreurs et propose des améliorations."""

        else:
            return """Tu es un assistant IA spécialisé dans l'analyse de puzzles ARC AGI. Réponds de manière claire et structurée."""
    
    def _generate_analysis_response(self, prompt: str) -> str:
        """Génère une réponse simulée pour l'analyse de puzzle."""
        return """
        Analyse du puzzle ARC AGI:
        
        TRANSFORMATIONS DÉTECTÉES:
        - Transformation de couleur: 3→4
        - Déplacement spatial: objets vers le centre
        
        PATTERNS IDENTIFIÉS:
        - Objets de taille 2x2
        - Alignement horizontal
        
        RÈGLES DÉDUITES:
        - Si objet couleur 3, alors changer en couleur 4
        - Si objet en bordure, alors déplacer vers centre
        
        GRILLE OUTPUT PROPOSÉE:
        [[4,4,0,0],
         [4,4,0,0],
         [0,0,4,4],
         [0,0,4,4]]
        
        RAISONNEMENT:
        1. Identifier les objets de couleur 3
        2. Appliquer la transformation de couleur
        3. Calculer les nouvelles positions centrées
        4. Générer la grille finale
        """
    
    def _generate_learning_response(self, prompt: str) -> str:
        """Génère une réponse simulée pour l'extraction d'insights."""
        return """
        Insights extraits de l'analyse réussie:
        
        PATTERNS GÉNÉRIQUES:
        - Transformation de couleur conditionnelle
        - Repositionnement basé sur la géométrie
        
        RÈGLES APPLICABLES:
        - Détecter les objets cohérents
        - Appliquer les transformations systématiquement
        
        CONFIDENCE: 0.85
        """
    
    def _generate_validation_response(self, prompt: str) -> str:
        """Génère une réponse simulée pour la validation."""
        return """
        Validation de la solution:
        
        COMPARAISON PIXEL PAR PIXEL:
        - Pixels corrects: 14/16 (87.5%)
        - Pixels incorrects: 2/16 (12.5%)
        
        DIAGNOSTIC:
        Position (0,2): Attendu 0, Obtenu 4
        Position (1,3): Attendu 0, Obtenu 4
        
        RECOMMANDATIONS:
        - Vérifier la logique de positionnement
        - Revoir les conditions de transformation
        """
    
    def _generate_default_response(self, prompt: str) -> str:
        """Génère une réponse simulée par défaut."""
        return f"Réponse simulée pour: {prompt[:50]}..."
    
    def _add_to_history(self, message: ChatMessage) -> None:
        """Ajoute un message à l'historique de conversation."""
        self.conversation_history.append(message)
        
        # Limiter la taille de l'historique
        if len(self.conversation_history) > self.config.max_history_length:
            # Garder les messages les plus récents
            self.conversation_history = self.conversation_history[-self.config.max_history_length:]
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """
        Récupère l'historique de conversation.
        
        Returns:
            List[Dict]: Liste des messages de conversation
        """
        return [msg.to_dict() for msg in self.conversation_history]
    
    def clear_history(self) -> None:
        """Efface l'historique de conversation."""
        self.conversation_history.clear()
    
    def get_session_info(self) -> Dict[str, Any]:
        """
        Récupère les informations de la session.
        
        Returns:
            Dict: Informations détaillées de la session
        """
        return {
            'session_id': self.session_id,
            'session_type': self.config.session_type,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat(),
            'last_activity': self.last_activity.isoformat(),
            'message_count': len(self.conversation_history),
            'config': {
                'timeout': self.config.timeout,
                'max_retries': self.config.max_retries,
                'enable_history': self.config.enable_history,
                'max_history_length': self.config.max_history_length
            }
        }
    
    def close_session(self) -> None:
        """Ferme la session et nettoie les ressources."""
        self.is_active = False
        if not self.config.enable_history:
            self.conversation_history.clear()
    
    def __enter__(self):
        """Support pour context manager."""
        if not self.is_active:
            self.create_new_session()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Support pour context manager."""
        self.close_session()


def create_chat_session(session_type: str = ChatSessionTypes.DEFAULT) -> ChatSession:
    """
    Factory function pour créer une session chat.
    
    Args:
        session_type: Type de session à créer
        
    Returns:
        ChatSession: Instance de session configurée
    """
    config = SessionConfig(session_type=session_type)
    session = ChatSession(config)
    session.create_new_session()
    return session