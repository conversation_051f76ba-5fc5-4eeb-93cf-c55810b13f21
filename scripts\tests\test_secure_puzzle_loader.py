"""
Tests unitaires pour le chargeur de puzzle sécurisé.

Ce module teste toutes les fonctionnalités du SecurePuzzleLoader,
incluant le chargement sécurisé, la validation et la gestion d'erreurs.
"""

import unittest
import tempfile
import json
import os
from pathlib import Path
from unittest.mock import patch, mock_open
import numpy as np

# Import du module à tester
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from scripts.core.secure_puzzle_loader import SecurePuzzleLoader
from scripts.core.data_models import PuzzleData
from scripts.core.exceptions import (
    PuzzleNotFoundError,
    InvalidPuzzleFormatError,
    PuzzleLoadingError
)
from scripts.core.constants import PuzzleSubsets


class TestSecurePuzzleLoader(unittest.TestCase):
    """Tests pour la classe SecurePuzzleLoader."""
    
    def setUp(self):
        """Configuration avant chaque test."""
        # Créer un répertoire temporaire pour les tests
        self.temp_dir = tempfile.mkdtemp()
        self.arc_data_dir = Path(self.temp_dir) / "arcdata"
        
        # Créer la structure de répertoires
        self.training_dir = self.arc_data_dir / "training"
        self.evaluation_dir = self.arc_data_dir / "evaluation"
        self.training_dir.mkdir(parents=True)
        self.evaluation_dir.mkdir(parents=True)
        
        # Données de puzzle valide pour les tests
        self.valid_puzzle_data = {
            "train": [
                {
                    "input": [[0, 1], [2, 3]],
                    "output": [[1, 0], [3, 2]]
                },
                {
                    "input": [[4, 5], [6, 7]],
                    "output": [[5, 4], [7, 6]]
                }
            ],
            "test": [
                {
                    "input": [[8, 9], [1, 2]],
                    "output": [[9, 8], [2, 1]]
                }
            ]
        }
        
        # Créer un fichier de puzzle valide
        self.puzzle_id = "test_puzzle"
        self.puzzle_file = self.training_dir / f"{self.puzzle_id}.json"
        with open(self.puzzle_file, 'w') as f:
            json.dump(self.valid_puzzle_data, f)
    
    def tearDown(self):
        """Nettoyage après chaque test."""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_init_valid_directory(self):
        """Test d'initialisation avec un répertoire valide."""
        loader = SecurePuzzleLoader(str(self.arc_data_dir))
        self.assertEqual(loader.arc_data_dir, self.arc_data_dir)
    
    def test_init_invalid_directory(self):
        """Test d'initialisation avec un répertoire inexistant."""
        invalid_dir = "/path/that/does/not/exist"
        with self.assertRaises(PuzzleLoadingError) as context:
            SecurePuzzleLoader(invalid_dir)
        
        self.assertIn("Répertoire de données ARC non trouvé", str(context.exception))
    
    def test_init_missing_subdirectory(self):
        """Test d'initialisation avec sous-répertoire manquant."""
        # Supprimer le répertoire evaluation
        import shutil
        shutil.rmtree(self.evaluation_dir)
        
        with self.assertRaises(PuzzleLoadingError) as context:
            SecurePuzzleLoader(str(self.arc_data_dir))
        
        self.assertIn("Sous-répertoire evaluation non trouvé", str(context.exception))
    
    def test_find_puzzle_file_in_training(self):
        """Test de localisation d'un puzzle dans training."""
        loader = SecurePuzzleLoader(str(self.arc_data_dir))
        file_path, subset = loader.find_puzzle_file(self.puzzle_id)
        
        self.assertEqual(file_path, self.puzzle_file)
        self.assertEqual(subset, "training")
    
    def test_find_puzzle_file_in_evaluation(self):
        """Test de localisation d'un puzzle dans evaluation."""
        # Créer un puzzle dans evaluation
        eval_puzzle_id = "eval_puzzle"
        eval_puzzle_file = self.evaluation_dir / f"{eval_puzzle_id}.json"
        with open(eval_puzzle_file, 'w') as f:
            json.dump(self.valid_puzzle_data, f)
        
        loader = SecurePuzzleLoader(str(self.arc_data_dir))
        file_path, subset = loader.find_puzzle_file(eval_puzzle_id)
        
        self.assertEqual(file_path, eval_puzzle_file)
        self.assertEqual(subset, "evaluation")
    
    def test_find_puzzle_file_not_found(self):
        """Test de localisation d'un puzzle inexistant."""
        loader = SecurePuzzleLoader(str(self.arc_data_dir))
        
        with self.assertRaises(PuzzleNotFoundError) as context:
            loader.find_puzzle_file("nonexistent_puzzle")
        
        self.assertEqual(context.exception.puzzle_id, "nonexistent_puzzle")
        # Vérifier que l'exception contient des informations sur les chemins recherchés
        self.assertIn("nonexistent_puzzle", str(context.exception))
    
    def test_load_puzzle_for_analysis_success(self):
        """Test de chargement réussi d'un puzzle."""
        loader = SecurePuzzleLoader(str(self.arc_data_dir))
        puzzle_data, hidden_solution = loader.load_puzzle_for_analysis(self.puzzle_id)
        
        # Vérifier les données du puzzle
        self.assertIsInstance(puzzle_data, PuzzleData)
        self.assertEqual(puzzle_data.puzzle_id, self.puzzle_id)
        self.assertEqual(puzzle_data.subset, "training")
        self.assertEqual(len(puzzle_data.train_examples), 2)
        self.assertIsNone(puzzle_data.hidden_solution)  # Toujours None pour sécurité
        
        # Vérifier l'input de test
        expected_test_input = np.array([[8, 9], [1, 2]])
        np.testing.assert_array_equal(puzzle_data.test_input, expected_test_input)
        
        # Vérifier la solution cachée
        self.assertIsNotNone(hidden_solution)
        expected_hidden = np.array([[9, 8], [2, 1]])
        np.testing.assert_array_equal(hidden_solution, expected_hidden)
    
    def test_load_puzzle_without_test_output(self):
        """Test de chargement d'un puzzle sans solution de test."""
        # Créer un puzzle sans output dans test
        puzzle_no_output = {
            "train": [
                {
                    "input": [[0, 1], [2, 3]],
                    "output": [[1, 0], [3, 2]]
                }
            ],
            "test": [
                {
                    "input": [[8, 9], [1, 2]]
                    # Pas d'output
                }
            ]
        }
        
        puzzle_id = "no_output_puzzle"
        puzzle_file = self.training_dir / f"{puzzle_id}.json"
        with open(puzzle_file, 'w') as f:
            json.dump(puzzle_no_output, f)
        
        loader = SecurePuzzleLoader(str(self.arc_data_dir))
        puzzle_data, hidden_solution = loader.load_puzzle_for_analysis(puzzle_id)
        
        self.assertIsInstance(puzzle_data, PuzzleData)
        self.assertIsNone(hidden_solution)  # Pas de solution disponible
    
    def test_validate_puzzle_format_missing_train(self):
        """Test de validation avec section train manquante."""
        invalid_data = {
            "test": [{"input": [[0, 1]], "output": [[1, 0]]}]
            # Pas de section train
        }
        
        puzzle_id = "invalid_puzzle"
        puzzle_file = self.training_dir / f"{puzzle_id}.json"
        with open(puzzle_file, 'w') as f:
            json.dump(invalid_data, f)
        
        loader = SecurePuzzleLoader(str(self.arc_data_dir))
        
        with self.assertRaises(InvalidPuzzleFormatError) as context:
            loader.load_puzzle_for_analysis(puzzle_id)
        
        self.assertIn("Clé manquante: 'train'", str(context.exception))
    
    def test_validate_puzzle_format_missing_test(self):
        """Test de validation avec section test manquante."""
        invalid_data = {
            "train": [{"input": [[0, 1]], "output": [[1, 0]]}]
            # Pas de section test
        }
        
        puzzle_id = "invalid_puzzle"
        puzzle_file = self.training_dir / f"{puzzle_id}.json"
        with open(puzzle_file, 'w') as f:
            json.dump(invalid_data, f)
        
        loader = SecurePuzzleLoader(str(self.arc_data_dir))
        
        with self.assertRaises(InvalidPuzzleFormatError) as context:
            loader.load_puzzle_for_analysis(puzzle_id)
        
        self.assertIn("Clé manquante: 'test'", str(context.exception))
    
    def test_validate_puzzle_format_empty_train(self):
        """Test de validation avec section train vide."""
        invalid_data = {
            "train": [],  # Liste vide
            "test": [{"input": [[0, 1]], "output": [[1, 0]]}]
        }
        
        puzzle_id = "invalid_puzzle"
        puzzle_file = self.training_dir / f"{puzzle_id}.json"
        with open(puzzle_file, 'w') as f:
            json.dump(invalid_data, f)
        
        loader = SecurePuzzleLoader(str(self.arc_data_dir))
        
        with self.assertRaises(InvalidPuzzleFormatError) as context:
            loader.load_puzzle_for_analysis(puzzle_id)
        
        self.assertIn("doit être une liste non vide", str(context.exception))
    
    def test_validate_grid_invalid_color(self):
        """Test de validation avec couleur invalide."""
        invalid_data = {
            "train": [
                {
                    "input": [[0, 10]],  # 10 est invalide (doit être 0-9)
                    "output": [[1, 0]]
                }
            ],
            "test": [{"input": [[0, 1]]}]
        }
        
        puzzle_id = "invalid_puzzle"
        puzzle_file = self.training_dir / f"{puzzle_id}.json"
        with open(puzzle_file, 'w') as f:
            json.dump(invalid_data, f)
        
        loader = SecurePuzzleLoader(str(self.arc_data_dir))
        
        with self.assertRaises(InvalidPuzzleFormatError) as context:
            loader.load_puzzle_for_analysis(puzzle_id)
        
        self.assertIn("doit être un entier entre 0 et 9", str(context.exception))
    
    def test_validate_grid_inconsistent_row_length(self):
        """Test de validation avec longueurs de lignes incohérentes."""
        invalid_data = {
            "train": [
                {
                    "input": [[0, 1], [2]],  # Deuxième ligne plus courte
                    "output": [[1, 0], [3, 2]]
                }
            ],
            "test": [{"input": [[0, 1]]}]
        }
        
        puzzle_id = "invalid_puzzle"
        puzzle_file = self.training_dir / f"{puzzle_id}.json"
        with open(puzzle_file, 'w') as f:
            json.dump(invalid_data, f)
        
        loader = SecurePuzzleLoader(str(self.arc_data_dir))
        
        with self.assertRaises(InvalidPuzzleFormatError) as context:
            loader.load_puzzle_for_analysis(puzzle_id)
        
        self.assertIn("a une longueur différente", str(context.exception))
    
    def test_validate_grid_too_large(self):
        """Test de validation avec grille trop grande."""
        # Créer une grille de 31x31 (dépasse la limite de 30)
        large_grid = [[0] * 31 for _ in range(31)]
        
        invalid_data = {
            "train": [
                {
                    "input": large_grid,
                    "output": [[1, 0]]
                }
            ],
            "test": [{"input": [[0, 1]]}]
        }
        
        puzzle_id = "invalid_puzzle"
        puzzle_file = self.training_dir / f"{puzzle_id}.json"
        with open(puzzle_file, 'w') as f:
            json.dump(invalid_data, f)
        
        loader = SecurePuzzleLoader(str(self.arc_data_dir))
        
        with self.assertRaises(InvalidPuzzleFormatError) as context:
            loader.load_puzzle_for_analysis(puzzle_id)
        
        self.assertIn("Hauteur invalide", str(context.exception))
    
    def test_load_puzzle_json_decode_error(self):
        """Test de gestion d'erreur JSON malformé."""
        puzzle_id = "malformed_puzzle"
        puzzle_file = self.training_dir / f"{puzzle_id}.json"
        
        # Écrire du JSON malformé
        with open(puzzle_file, 'w') as f:
            f.write('{"train": [invalid json}')
        
        loader = SecurePuzzleLoader(str(self.arc_data_dir))
        
        with self.assertRaises(InvalidPuzzleFormatError) as context:
            loader.load_puzzle_for_analysis(puzzle_id)
        
        self.assertIn("Erreur de parsing JSON", str(context.exception))
    
    def test_load_puzzle_file_read_error(self):
        """Test de gestion d'erreur de lecture de fichier."""
        loader = SecurePuzzleLoader(str(self.arc_data_dir))
        
        # Simuler une erreur de lecture avec mock
        with patch('builtins.open', mock_open()) as mock_file:
            mock_file.side_effect = IOError("Permission denied")
            
            with self.assertRaises(PuzzleLoadingError) as context:
                loader.load_puzzle_for_analysis(self.puzzle_id)
            
            self.assertIn("Erreur de lecture du fichier", str(context.exception))
    
    def test_get_available_puzzles_all_subsets(self):
        """Test de récupération de tous les puzzles disponibles."""
        # Créer des puzzles supplémentaires
        eval_puzzle_id = "eval_puzzle"
        eval_puzzle_file = self.evaluation_dir / f"{eval_puzzle_id}.json"
        with open(eval_puzzle_file, 'w') as f:
            json.dump(self.valid_puzzle_data, f)
        
        loader = SecurePuzzleLoader(str(self.arc_data_dir))
        available = loader.get_available_puzzles()
        
        self.assertIn("training", available)
        self.assertIn("evaluation", available)
        self.assertIn(self.puzzle_id, available["training"])
        self.assertIn(eval_puzzle_id, available["evaluation"])
    
    def test_get_available_puzzles_specific_subset(self):
        """Test de récupération des puzzles d'un sous-ensemble spécifique."""
        loader = SecurePuzzleLoader(str(self.arc_data_dir))
        available = loader.get_available_puzzles("training")
        
        self.assertIn("training", available)
        self.assertNotIn("evaluation", available)
        self.assertIn(self.puzzle_id, available["training"])
    
    def test_get_available_puzzles_empty_directory(self):
        """Test de récupération avec répertoire vide."""
        # Supprimer tous les fichiers
        os.remove(self.puzzle_file)
        
        loader = SecurePuzzleLoader(str(self.arc_data_dir))
        available = loader.get_available_puzzles("training")
        
        self.assertEqual(available["training"], [])
    
    def test_security_hidden_solution_never_in_puzzle_data(self):
        """Test de sécurité: la solution cachée n'est jamais dans PuzzleData."""
        loader = SecurePuzzleLoader(str(self.arc_data_dir))
        puzzle_data, hidden_solution = loader.load_puzzle_for_analysis(self.puzzle_id)
        
        # Vérifier que la solution n'est jamais exposée dans puzzle_data
        self.assertIsNone(puzzle_data.hidden_solution)
        
        # Mais qu'elle est disponible séparément
        self.assertIsNotNone(hidden_solution)
        
        # Vérifier qu'aucune référence à la solution n'existe dans puzzle_data
        self.assertNotEqual(id(puzzle_data.test_input), id(hidden_solution))
    
    def test_data_separation_integrity(self):
        """Test d'intégrité de la séparation des données."""
        loader = SecurePuzzleLoader(str(self.arc_data_dir))
        puzzle_data, hidden_solution = loader.load_puzzle_for_analysis(self.puzzle_id)
        
        # Vérifier que les données d'entraînement sont complètes
        self.assertEqual(len(puzzle_data.train_examples), 2)
        for example in puzzle_data.train_examples:
            self.assertIn('input', example)
            self.assertIn('output', example)
        
        # Vérifier que l'input de test est présent
        self.assertIsNotNone(puzzle_data.test_input)
        
        # Vérifier que la solution cachée est séparée
        self.assertIsNotNone(hidden_solution)
        
        # Vérifier que modifier puzzle_data n'affecte pas hidden_solution
        original_hidden = hidden_solution.copy()
        puzzle_data.test_input[0, 0] = 999  # Modification
        np.testing.assert_array_equal(hidden_solution, original_hidden)


class TestSecurePuzzleLoaderEdgeCases(unittest.TestCase):
    """Tests pour les cas limites du SecurePuzzleLoader."""
    
    def setUp(self):
        """Configuration avant chaque test."""
        self.temp_dir = tempfile.mkdtemp()
        self.arc_data_dir = Path(self.temp_dir) / "arcdata"
        self.training_dir = self.arc_data_dir / "training"
        self.evaluation_dir = self.arc_data_dir / "evaluation"
        self.training_dir.mkdir(parents=True)
        self.evaluation_dir.mkdir(parents=True)
    
    def tearDown(self):
        """Nettoyage après chaque test."""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_minimal_valid_puzzle(self):
        """Test avec un puzzle minimal mais valide."""
        minimal_puzzle = {
            "train": [
                {
                    "input": [[0]],
                    "output": [[1]]
                }
            ],
            "test": [
                {
                    "input": [[2]]
                }
            ]
        }
        
        puzzle_id = "minimal_puzzle"
        puzzle_file = self.training_dir / f"{puzzle_id}.json"
        with open(puzzle_file, 'w') as f:
            json.dump(minimal_puzzle, f)
        
        loader = SecurePuzzleLoader(str(self.arc_data_dir))
        puzzle_data, hidden_solution = loader.load_puzzle_for_analysis(puzzle_id)
        
        self.assertIsInstance(puzzle_data, PuzzleData)
        self.assertEqual(len(puzzle_data.train_examples), 1)
        self.assertIsNone(hidden_solution)  # Pas de solution dans test
    
    def test_maximum_size_puzzle(self):
        """Test avec un puzzle de taille maximale."""
        # Créer des grilles de 30x30 (taille maximale)
        max_grid = [[i % 10 for i in range(30)] for _ in range(30)]
        
        max_puzzle = {
            "train": [
                {
                    "input": max_grid,
                    "output": max_grid
                }
            ],
            "test": [
                {
                    "input": max_grid,
                    "output": max_grid
                }
            ]
        }
        
        puzzle_id = "max_puzzle"
        puzzle_file = self.training_dir / f"{puzzle_id}.json"
        with open(puzzle_file, 'w') as f:
            json.dump(max_puzzle, f)
        
        loader = SecurePuzzleLoader(str(self.arc_data_dir))
        puzzle_data, hidden_solution = loader.load_puzzle_for_analysis(puzzle_id)
        
        self.assertIsInstance(puzzle_data, PuzzleData)
        self.assertEqual(puzzle_data.test_input.shape, (30, 30))
        self.assertEqual(hidden_solution.shape, (30, 30))


if __name__ == '__main__':
    unittest.main()