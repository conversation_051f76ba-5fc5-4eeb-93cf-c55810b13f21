#!/usr/bin/env python3
"""
Script de test pour vérifier le générateur de prompts amélioré
"""

import subprocess
import os

def test_enhanced_prompt():
    """Teste le générateur de prompts avec les insights"""
    
    # Tester avec le puzzle 2204b7a8
    cmd = [
        "python", "arc_prompt_generator4.py",
        "--taskid", "2204b7a8",
        "--subset", "training",
        "--arc-data-dir", "../arc-puzzle/arcdata/"
    ]
    
    print("🧪 Test du générateur de prompts amélioré...")
    print(f"Commande: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print("✅ Génération réussie !")
        print(result.stdout)
        
        # Vérifier que le fichier contient les insights
        prompt_file = "arc_results/training/2204b7a8_prompt.txt"
        if os.path.exists(prompt_file):
            with open(prompt_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Vérifications
            checks = [
                ("🔍 INSIGHTS CRITIQUES", "Section insights présente"),
                ("COULEUR CIBLE", "Identification couleur cible"),
                ("STRUCTURE:", "Analyse structure"),
                ("TRANSFORMATION:", "Type transformation"),
                ("distance Manhattan", "Règle de proximité"),
                ("💡 CES INFORMATIONS", "Section conseils")
            ]
            
            print("\n📋 Vérifications du contenu:")
            for check, description in checks:
                if check in content:
                    print(f"✅ {description}")
                else:
                    print(f"❌ {description}")
            
            print(f"\n📄 Taille du prompt: {len(content)} caractères")
            print(f"📄 Nombre de lignes: {len(content.splitlines())}")
            
        else:
            print(f"❌ Fichier prompt non trouvé: {prompt_file}")
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur lors de l'exécution: {e}")
        print(f"Sortie d'erreur: {e.stderr}")
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")

if __name__ == "__main__":
    test_enhanced_prompt()