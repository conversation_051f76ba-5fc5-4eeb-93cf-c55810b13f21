#!/usr/bin/env python3
"""
Script simple pour générer les analyses ARC et les organiser proprement
"""

import sys
import json
import numpy as np
import argparse
import os
from pathlib import Path

# Ajouter les chemins nécessaires
script_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.abspath(os.path.join(script_dir, '..', '..'))

# Ajouter le répertoire courant pour arc_analyzer.py
sys.path.insert(0, script_dir)
sys.path.insert(0, root_dir)
sys.path.insert(0, os.path.join(root_dir, 'analyze_grid'))
sys.path.insert(0, os.path.join(root_dir, 'models'))
sys.path.insert(0, os.path.join(root_dir, 'src'))

from arc_analyzer import ARCAnalyzer

def find_task_file(taskid):
    """Trouve le fichier de tâche dans arcdata/training ou arcdata/evaluation"""
    
    # Chercher dans training
    training_file = Path(f"../arc-puzzle/arcdata/training/{taskid}.json")
    if training_file.exists():
        return training_file, "training"
    
    # Chercher dans evaluation
    evaluation_file = Path(f"../arc-puzzle/arcdata/evaluation/{taskid}.json")
    if evaluation_file.exists():
        return evaluation_file, "evaluation"
    
    return None, None

def generate_analysis_for_task(taskid):
    """Génère l'analyse pour une tâche donnée"""
    
    print(f"🔍 Recherche de la tâche {taskid}...")
    
    # Trouver le fichier de tâche
    task_file, subset = find_task_file(taskid)
    if not task_file:
        print(f"❌ Tâche {taskid} non trouvée dans ../arc-puzzle/arcdata/training/ ou ../arc-puzzle/arcdata/evaluation/")
        return False
    
    print(f"✅ Tâche trouvée dans {subset}: {task_file}")
    
    # Charger les données de la tâche
    with open(task_file, 'r') as f:
        puzzle_data = json.load(f)
    
    print(f"📊 Analyse en cours...")
    
    # Analyser avec ARCAnalyzer
    analyzer = ARCAnalyzer()
    analysis = analyzer.analyze_puzzle(puzzle_data)
    
    # Créer la structure de données complète
    analysis_data = {
        'puzzle_id': taskid,
        'subset': subset,
        'raw_analysis': analysis
    }
    
    # Créer le répertoire de destination
    output_dir = Path(f"analysis_data/{subset}")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Sauvegarder l'analyse en utilisant une sérialisation plus robuste
    output_file = output_dir / f"{taskid}_analysis.json"
    
    # Convertir les types NumPy avant la sérialisation JSON
    from arc_analyzer import convert_numpy_types
    analysis_data_clean = convert_numpy_types(analysis_data)
    
    # Convertir en JSON string
    json_str = json.dumps(analysis_data_clean, indent=2)
    
    with open(output_file, 'w') as f:
        f.write(json_str)
    
    # Calculer la taille
    size_kb = output_file.stat().st_size / 1024
    
    print(f"💾 Analyse sauvegardée: {output_file}")
    print(f"📏 Taille: {size_kb:.1f} KB")
    print(f"📋 Subset: {subset}")
    
    return True

def list_available_tasks():
    """Liste toutes les tâches disponibles dans arcdata"""
    
    training_dir = Path("../arc-puzzle/arcdata/training")
    evaluation_dir = Path("../arc-puzzle/arcdata/evaluation")
    
    tasks = {"training": [], "evaluation": []}
    
    if training_dir.exists():
        for file in training_dir.glob("*.json"):
            if len(file.stem) == 8:  # Format taskid
                tasks["training"].append(file.stem)
    
    if evaluation_dir.exists():
        for file in evaluation_dir.glob("*.json"):
            if len(file.stem) == 8:  # Format taskid
                tasks["evaluation"].append(file.stem)
    
    return tasks

def list_generated_analyses():
    """Liste les analyses déjà générées"""
    
    analyses = {"training": [], "evaluation": []}
    
    for subset in ["training", "evaluation"]:
        analysis_dir = Path(f"analysis_data/{subset}")
        if analysis_dir.exists():
            for file in analysis_dir.glob("*_analysis.json"):
                taskid = file.stem.replace("_analysis", "")
                analyses[subset].append(taskid)
    
    return analyses

def main():
    parser = argparse.ArgumentParser(description="Générateur d'analyses ARC")
    parser.add_argument("--taskid", type=str, help="ID de la tâche à analyser")
    parser.add_argument("--list", action="store_true", help="Lister les tâches disponibles")
    parser.add_argument("--status", action="store_true", help="Afficher le statut des analyses")
    
    args = parser.parse_args()
    
    if args.list:
        print("📋 TÂCHES DISPONIBLES DANS ARCDATA")
        print("=" * 40)
        
        tasks = list_available_tasks()
        
        print(f"\n🎓 Training ({len(tasks['training'])} tâches):")
        for i, taskid in enumerate(sorted(tasks['training'])[:10]):  # Afficher les 10 premières
            print(f"  {taskid}")
        if len(tasks['training']) > 10:
            print(f"  ... et {len(tasks['training']) - 10} autres")
        
        print(f"\n🧪 Evaluation ({len(tasks['evaluation'])} tâches):")
        for i, taskid in enumerate(sorted(tasks['evaluation'])[:10]):  # Afficher les 10 premières
            print(f"  {taskid}")
        if len(tasks['evaluation']) > 10:
            print(f"  ... et {len(tasks['evaluation']) - 10} autres")
        
        return
    
    if args.status:
        print("📊 STATUT DES ANALYSES GÉNÉRÉES")
        print("=" * 35)
        
        tasks = list_available_tasks()
        analyses = list_generated_analyses()
        
        for subset in ["training", "evaluation"]:
            total = len(tasks[subset])
            generated = len(analyses[subset])
            percentage = (generated / total * 100) if total > 0 else 0
            
            print(f"\n{subset.title()}:")
            print(f"  Tâches disponibles: {total}")
            print(f"  Analyses générées: {generated}")
            print(f"  Progression: {percentage:.1f}%")
            
            if generated > 0:
                print(f"  Exemples générés: {', '.join(sorted(analyses[subset])[:5])}")
                if generated > 5:
                    print(f"                    ... et {generated - 5} autres")
        
        return
    
    if args.taskid:
        print("🚀 GÉNÉRATEUR D'ANALYSES ARC")
        print("=" * 30)
        
        success = generate_analysis_for_task(args.taskid)
        
        if success:
            print(f"\n✅ Analyse générée avec succès !")
            print(f"🌐 Vous pouvez maintenant visualiser avec:")
            print(f"   python launch_web_interface.py server")
        else:
            print(f"\n❌ Échec de la génération")
        
        return
    
    # Affichage de l'aide par défaut
    parser.print_help()
    print(f"\n🎯 Exemples d'utilisation:")
    print(f"  python generate_analysis.py --list")
    print(f"  python generate_analysis.py --status")
    print(f"  python generate_analysis.py --taskid 007bbfb7")

if __name__ == "__main__":
    main()