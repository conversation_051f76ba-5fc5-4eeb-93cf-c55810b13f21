﻿import json
import requests
import argparse
import os
import time
from datetime import datetime

def grid_to_text(grid, title):
    """Convert grid to human-readable text format"""
    if not grid or not isinstance(grid, list) or not all(isinstance(row, list) for row in grid):
        return f"{title} (invalid grid format)\n"
        
    text = f"{title} ({len(grid)}x{len(grid[0])} grid):\n"
    for row in grid:
        text += " ".join(str(x) for x in row) + "\n"
    return text

def arc_json_to_text(task_id, subset="training", arc_data_dir="arcdata"):
    """Convert ARC JSON puzzle to structured text prompt"""
    # Utilise os.path.join pour gérer correctement les séparateurs de chemin
    # Charger les données ARC
    json_path = os.path.join(arc_data_dir, subset, f"{task_id}.json")
    
    if not os.path.exists(json_path):
        # Message d'erreur plus explicite avec la structure attendue
        raise FileNotFoundError(
            f"Puzzle file not found: {json_path}\n\n"
            "Please ensure your ARC data is structured as follows:\n"
            f"{arc_data_dir}/\n"
            f"├── {subset}/\n"
            f"│   └── {task_id}.json\n"
            f"└── {'evaluation' if subset == 'training' else 'training'}/\n\n"
            "Download ARC dataset from https://github.com/fchollet/ARC"
        )
    
    with open(json_path, 'r') as f:
        puzzle = json.load(f)

    # Construire le prompt au format Qwen3
    prompt = "<|im_start|>system\n"
    prompt += "You are an ARC AGI puzzle expert. You MUST use THINKING MODE for this task.\n"
    prompt += "Think step by step, verify your rule, then apply it. Show your complete reasoning.\n"
    prompt += "<|im_end|>\n"
    prompt += "<|im_start|>user\n"
    
    # Ajouter les exemples d'entraînement
    prompt += "Solve this ARC puzzle by analyzing the pattern transformation:\n\n"
    
    for i, example in enumerate(puzzle['train']):
        prompt += f"Example {i+1}:\n"
        prompt += grid_to_text(example['input'], "Input grid")
        prompt += grid_to_text(example['output'], "Output grid")
        prompt += "\n"
    
    # Ajouter l'exemple de test
    prompt += "Apply the rule to this test input:\n"
    prompt += grid_to_text(puzzle['test'][0]['input'], "Test input grid")
    prompt += "\n\n"
    prompt += "IMPORTANT: Use structured thinking:\n"
    prompt += "1. OBSERVE: Describe input/output patterns\n"
    prompt += "2. HYPOTHESIZE: Formulate the transformation rule\n"
    prompt += "3. VERIFY: Check rule against all examples\n"
    prompt += "4. APPLY: Transform the test input\n"
    prompt += "5. OUTPUT: Provide ONLY the grid numbers\n"
    prompt += "<|im_end|>\n"
    prompt += "<|im_start|>assistant\n"
    prompt += "Okay, let me think step by step...\n"

    #prompt = """<|im_start|>system
    #You are solving ARC AGI puzzles. You MUST use structured thinking mode for this task.
    #Activate deep reasoning: think step by step, verify your rule, then apply it.
    #<|im_end|>
    #<|im_start|>user
    #You are an expert in pattern recognition for ARC AGI puzzles. Solve this puzzle using THOROUGH reasoning:

    #1. OBSERVE: Describe each input and output grid precisely
    #2. COMPARE: Identify what changes between input and output
    #3. HYPOTHESIZE: Formulate a precise rule that explains the transformation
    #4. VERIFY: Check if your rule works for ALL examples
    #5. APPLY: Use the rule to transform the test input

    #[Examples section remains the same]

    #Now apply this structured reasoning process to the test input:
    #[Test input section remains the same]

    #IMPORTANT: Show your complete reasoning before the final answer.
    #Final answer (grid only after complete reasoning):<|im_end|>
    #<|im_start|>assistant
    #Okay, let me think step by step..."""

    #prompt = """You are an expert in pattern recognition for ARC AGI puzzles. Analyze the transformation rule from the input-output #examples using this structured approach:

    #1. OBSERVE: Describe each input and output grid precisely
    #2. COMPARE: Identify what changes between input and output
    #3. HYPOTHESIZE: Formulate a precise rule that explains the transformation
    #4. TEST: Verify your rule with all examples
    #5. APPLY: Use the rule to transform the test input

    #[Examples section remains the same]

    #Now apply this structured reasoning process to the test input:
    #Test input section remains the same]

    #Final answer (grid only after complete reasoning):"""
    
    #prompt = "You are an expert in pattern recognition specialized in ARC AGI puzzles. Analyze the transformation rule from the #input-output examples, then apply it to the test input.\n\n"
    
    # Add training examples
    for i, example in enumerate(puzzle['train']):
        prompt += f"Example {i+1}:\n"
        prompt += grid_to_text(example['input'], "Input")
        prompt += grid_to_text(example['output'], "Output")
        prompt += "\n"
    
    # Add test example
    prompt += "Now apply this rule to the test input:\n"
    prompt += grid_to_text(puzzle['test'][0]['input'], "Test Input")
    prompt += "\n\nOutput grid (respond ONLY with the grid numbers, one row per line):"
    
    return prompt

def query_ollama(prompt, model="qwen3-arc:latest", num_ctx=16384, num_gpu=35, timeout=1200):
    """Query Ollama API with Qwen3-specific parameters"""
    print(f"🔄 Sending request to Ollama (timeout: {timeout}s)...")
    
    try:
        response = requests.post(
            'http://localhost:11434/api/generate',
            json={
                'model': model,
                'prompt': prompt,
                'stream': False,
                'options': {
                    'num_ctx': num_ctx,
                    'num_gpu': num_gpu
                }
            },
            timeout=timeout
        )
        
        response.raise_for_status()
        return response.json()['response']
    
    except requests.exceptions.ReadTimeout:
        print("⚠️ Request timed out. The model is still processing - try these solutions:")
        print("   1. Increase timeout further (edit script to set timeout=1800)")
        print("   2. First load the model with a simple prompt: ollama run phi4-reasoning 'Hello'")
        print("   3. Reduce num_gpu_layers to 30 temporarily for faster loading")
        raise Exception("Ollama request timed out (15 minutes). The model may still be loading.") from None

    except requests.exceptions.RequestException as e:
        # Amélioration du message d'erreur
        error_msg = f"Ollama API error: {str(e)}"
        if "404" in str(e):
            error_msg += "\n\nThe model might not exist. Check with 'ollama list'"
        raise Exception(error_msg) from e

    except (KeyError, json.JSONDecodeError) as e:
        raise Exception(f"Invalid response from Ollama: {str(e)}") from e
def parse_grid_response(response_text, expected_size=None):
    """Parse the model's grid response"""
    # Extract grid part from response
    lines = [line.strip() for line in response_text.split('\n') if ' ' in line or all(c.isdigit() or c in ',.' for c in line)]
    
    grid = []
    for line in lines:
        # Clean and extract numbers
        numbers = [c for c in line if c.isdigit() or c.isspace()]
        if numbers:
            row = [int(x) for x in ''.join(numbers).split() if x]
            if row:  # Only add non-empty rows
                grid.append(row)
    
    # Validate grid size if expected size is provided
    if expected_size and len(grid) == expected_size[0] and all(len(row) == expected_size[1] for row in grid):
        return grid
    
    # Fallback: take first square subgrid
    if grid:
        min_dim = min(len(grid), min(len(row) for row in grid))
        if min_dim > 0:
            return [row[:min_dim] for row in grid[:min_dim]]
    
    return None

def solve_arc_task(task_id, subset="training", arc_data_dir="arcdata", output_dir="arc_results", model="qwen3-arc:latest"):
    """Solve a single ARC task with the specified model"""
    start_time = time.time()
    
    # Vérification préalable de l'existence du répertoire
    subset_dir = os.path.join(arc_data_dir, subset)
    if not os.path.exists(subset_dir):
        raise FileNotFoundError(
            f"Subset directory not found: {subset_dir}\n\n"
            "Please create the directory structure:\n"
            f"{arc_data_dir}/\n"
            f"├── training/\n"
            f"└── evaluation/\n\n"
            "Download ARC dataset from https://github.com/fchollet/ARC"
        )
    
    try:
        # Get puzzle data to determine expected grid size
        json_path = os.path.join(arc_data_dir, subset, f"{task_id}.json")
        with open(json_path, 'r') as f:
            puzzle = json.load(f)
        
        # Expected output size (from first training example output)
        expected_size = (len(puzzle['train'][0]['output']), len(puzzle['train'][0]['output'][0]))
        
        # Generate prompt
        prompt = arc_json_to_text(task_id, subset, arc_data_dir)
        
        # Query model
        response = query_ollama(
            prompt,
            model=model,
            num_ctx=16384,  # Toujours utiliser un contexte suffisant
            num_gpu=35,  # Plus de couches GPU possibles grâce à la taille réduite
            timeout = 600
        )
        
        # Parse response
        predicted_grid = parse_grid_response(response, expected_size)
        
        # Save results with model information
        os.makedirs(output_dir, exist_ok=True)
        result = {
            "task_id": task_id,
            "subset": subset,
            "model_used": model,
            "prompt": prompt,
            "response": response,
            "predicted_grid": predicted_grid,
            "expected_size": expected_size,
            "processing_time": round(time.time() - start_time, 2),
            "timestamp": datetime.now().isoformat()
        }
        
        # Create subset-specific output directory
        subset_output_dir = os.path.join(output_dir, subset)
        os.makedirs(subset_output_dir, exist_ok=True)
        
        with open(os.path.join(subset_output_dir, f"{task_id}_result.json"), 'w') as f:
            json.dump(result, f, indent=2)
            
        return result
    
    except Exception as e:
        error_result = {
            "task_id": task_id,
            "subset": subset,
            "model_used": model,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
        
        # Create subset-specific output directory
        subset_output_dir = os.path.join(output_dir, subset)
        os.makedirs(subset_output_dir, exist_ok=True)
        
        with open(os.path.join(subset_output_dir, f"{task_id}_error.json"), 'w') as f:
            json.dump(error_result, f, indent=2)
            
        return error_result

def batch_solve(task_ids, subset="training", arc_data_dir="arc_data", output_dir="arc_results", model="qwen3-arc:latest"):
    """Solve multiple ARC tasks in sequence"""
    results = []
    for task_id in task_ids:
        print(f"\n🚀 Solving task: {task_id} (subset: {subset})")
        result = solve_arc_task(task_id, subset, arc_data_dir, output_dir, model)
        
        if "error" in result:
            print(f"❌ Error solving {task_id}: {result['error']}")
        else:
            print(f"✅ Successfully solved {task_id} in {result['processing_time']}s")
            if result["predicted_grid"]:
                print("Predicted grid:")
                for row in result["predicted_grid"]:
                    print(" ".join(str(x) for x in row))
            else:
                print("⚠️ Failed to parse grid from model response")
        
        # Small delay between requests
        time.sleep(1)
        
        results.append(result)
    
    # Summary
    success = sum(1 for r in results if "predicted_grid" in r and r["predicted_grid"])
    print(f"\n📊 Batch complete: {success}/{len(results)} tasks solved successfully")
    
    return results

def get_all_task_ids(subset, arc_data_dir="arcdata"):
    """Get all task IDs from a subset directory"""
    subset_dir = os.path.join(arc_data_dir, subset)
    if not os.path.exists(subset_dir):
        raise FileNotFoundError(f"Subset directory not found: {subset_dir}\n\n" +
                               "Please create the directory structure:\n" +
                               f"{arc_data_dir}/\n" +
                               f"├── training/\n" +
                               f"└── evaluation/")
    
    task_ids = []
    for filename in os.listdir(subset_dir):
        if filename.endswith('.json'):
            task_ids.append(os.path.splitext(filename)[0])
    
    if not task_ids:
        print(f"⚠️ Warning: No JSON files found in {subset_dir}")
    
    return task_ids

def check_arc_data_structure(arc_data_dir="arcdata"):
    """Check if ARC data directory has the required structure"""
    required_subsets = ["training", "evaluation"]
    found_subsets = []
    
    for subset in required_subsets:
        subset_dir = os.path.join(arc_data_dir, subset)
        if os.path.exists(subset_dir):
            json_files = [f for f in os.listdir(subset_dir) if f.endswith('.json')]
            if json_files:
                found_subsets.append(f"{subset} ({len(json_files)} files)")
    
    if not found_subsets:
        return (
            "❌ ARC data directory structure is not set up correctly.\n\n"
            "Expected structure:\n"
            f"{arc_data_dir}/\n"
            "├── training/ (with .json files)\n"
            "└── evaluation/ (with .json files)\n\n"
            "Please download the ARC dataset from https://github.com/fchollet/ARC"
        )
    else:
        return (
            "✅ ARC data directory structure is correct.\n"
            f"Found subsets: {', '.join(found_subsets)}"
        )

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description='Solve ARC AGI puzzles using ollama model',
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument('--taskid', type=str, help='Single task ID to solve (e.g., 025d127b)')
    parser.add_argument('--model', type=str, default='phi4-arc:latest', help='Model to use for solving puzzles')
    parser.add_argument('--batch', nargs='+', help='Multiple task IDs to solve')
    parser.add_argument('--all', action='store_true', help='Solve all tasks in the specified subset')
    parser.add_argument('--subset', type=str, choices=['training', 'evaluation'], default='training', 
                        help='ARC subset to use (training or evaluation)')
    parser.add_argument('--dir', type=str, default='arcdata', help='Directory containing ARC data with training/evaluation subdirectories')
    parser.add_argument('--output', type=str, default='arc_results', help='Directory for results')
    parser.add_argument('--check-structure', action='store_true', help='Check ARC data directory structure and exit')
    
    args = parser.parse_args()
    
    # Vérifie la structure si demandé
    if args.check_structure:
        print(check_arc_data_structure(args.dir))
        exit(0)
    
    # Vérifie la structure de base
    if not (args.taskid or args.batch or args.all):
        print(parser.format_help())
        print("\n" + check_arc_data_structure(args.dir))
        print("\nExample usage:")
        print("  python arc_solver.py --taskid 025d127b")
        print("  python arc_solver.py --taskid 025d127b --subset evaluation")
        print("  python arc_solver.py --all --subset training")
        print("  python arc_solver.py --check-structure")
        exit(1)
    
    # Determine task IDs to solve
    if args.taskid:
        task_ids = [args.taskid]
    elif args.batch:
        task_ids = args.batch
    elif args.all:
        try:
            task_ids = get_all_task_ids(args.subset, args.dir)
            if not task_ids:
                print(f"❌ No tasks found in {args.subset} subset. Check your data directory.")
                exit(1)
            print(f"Found {len(task_ids)} tasks in {args.subset} subset")
        except FileNotFoundError as e:
            print(f"❌ Error: {str(e)}")
            print("\nRun with --check-structure to verify your data directory")
            exit(1)
    
    # Solve the tasks
    try:
        batch_solve(task_ids, args.subset, args.dir, args.output)
    except Exception as e:
        print(f"❌ Critical error: {str(e)}")
        print("\nRun with --check-structure to verify your data directory")
        exit(1)