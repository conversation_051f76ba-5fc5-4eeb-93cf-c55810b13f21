#!/usr/bin/env python3
"""
Extracteur d'insights clés à partir des fichiers d'analyse ARC AGI
Identifie les informations critiques qui auraient aidé à résoudre le puzzle
"""

import json
import os
from pathlib import Path

def extract_key_insights(analysis_data):
    """Extrait les insights clés du fichier d'analyse JSON"""
    insights = {
        "color_transformations": {},
        "object_structure": {},
        "spatial_patterns": {},
        "transformation_type": {},
        "change_statistics": {},
        "critical_observations": []
    }
    
    try:
        raw_analysis = analysis_data.get("raw_analysis", {})
        
        # 1. Analyse des changements de couleurs
        if "diff_analysis" in raw_analysis and "common_color_changes" in raw_analysis["diff_analysis"]:
            color_changes = raw_analysis["diff_analysis"]["common_color_changes"]
            insights["color_transformations"] = {
                "changes": color_changes,
                "total_transformations": sum(color_changes.values()),
                "most_frequent": max(color_changes.items(), key=lambda x: x[1]) if color_changes else None
            }
            
            # Identifier si une couleur spécifique est toujours transformée
            transformed_colors = set()
            for change in color_changes.keys():
                if "→" in change:
                    from_color = change.split("→")[0]
                    transformed_colors.add(from_color)
            
            if len(transformed_colors) == 1:
                color = list(transformed_colors)[0]
                insights["critical_observations"].append(
                    f"COULEUR CIBLE: Seule la couleur {color} est transformée dans tous les exemples"
                )
        
        # 2. Structure des objets
        if "objects" in raw_analysis and "input_objects" in raw_analysis["objects"]:
            object_sizes = []
            for example in raw_analysis["objects"]["input_objects"]:
                sizes = [obj.get("size", 0) for obj in example]
                object_sizes.append(sizes)
            
            # Analyser la distribution des tailles d'objets
            all_sizes = [size for sizes in object_sizes for size in sizes]
            if all_sizes:
                max_size = max(all_sizes)
                min_size = min(all_sizes)
                size_distribution = {}
                for size in all_sizes:
                    size_distribution[size] = size_distribution.get(size, 0) + 1
                
                # Identifier les tailles les plus fréquentes
                most_common_sizes = sorted(size_distribution.items(), key=lambda x: x[1], reverse=True)
                
                insights["object_structure"] = {
                    "size_distribution": size_distribution,
                    "max_size": max_size,
                    "min_size": min_size,
                    "most_common_sizes": most_common_sizes[:3],  # Top 3
                    "size_variety": len(size_distribution)
                }
                
                # Observations basées sur la distribution réelle
                if len(most_common_sizes) >= 2:
                    large_size, large_count = most_common_sizes[0]
                    small_size, small_count = most_common_sizes[-1]
                    
                    if large_size > small_size * 5:  # Si il y a une grande différence de taille
                        insights["critical_observations"].append(
                            f"STRUCTURE: Objets de tailles très différentes détectés (max: {max_size}, min: {min_size})"
                        )
        
        # 3. Patterns spatiaux
        if "spatial_relations" in raw_analysis and "alignment_patterns" in raw_analysis["spatial_relations"]:
            alignment = raw_analysis["spatial_relations"]["alignment_patterns"]
            insights["spatial_patterns"] = {
                "alignment_data": alignment,
                "has_horizontal_borders": any(p.get("horizontal", 0) > 0 for p in alignment),
                "has_vertical_borders": any(p.get("vertical", 0) > 0 for p in alignment)
            }
            
            # Déterminer les patterns d'alignement dominants
            alignment_types = []
            for pattern in alignment:
                if pattern.get("horizontal", 0) > pattern.get("vertical", 0):
                    alignment_types.append("horizontal")
                elif pattern.get("vertical", 0) > pattern.get("horizontal", 0):
                    alignment_types.append("vertical")
                else:
                    alignment_types.append("mixed")
            
            if alignment_types:
                insights["critical_observations"].append(
                    f"ALIGNEMENT: Patterns détectés - {', '.join(set(alignment_types))}"
                )
        
        # 4. Type de transformation
        if "transformations" in raw_analysis:
            transformations = raw_analysis["transformations"]
            insights["transformation_type"] = transformations
            
            if "color" in transformations and "color_removal" in transformations["color"]:
                insights["critical_observations"].append(
                    "TRANSFORMATION: Suppression de couleur détectée (pas de déplacement géométrique)"
                )
            
            if "structural" in transformations and "in_place_transformation" in transformations["structural"]:
                insights["critical_observations"].append(
                    "TRANSFORMATION: Modification sur place (pas de déplacement d'objets)"
                )
        
        # 5. Statistiques des changements
        if "diff_analysis" in raw_analysis and "train_diffs" in raw_analysis["diff_analysis"]:
            diffs = raw_analysis["diff_analysis"]["train_diffs"]
            change_percentages = [d.get("change_percentage", 0) for d in diffs]
            total_changes = [d.get("total_changes", 0) for d in diffs]
            
            insights["change_statistics"] = {
                "change_percentages": change_percentages,
                "total_changes": total_changes,
                "average_change_percentage": sum(change_percentages) / len(change_percentages) if change_percentages else 0,
                "low_change_rate": all(p <= 10 for p in change_percentages)
            }
            
            if insights["change_statistics"]["low_change_rate"]:
                insights["critical_observations"].append(
                    f"CHANGEMENTS: Très peu de pixels modifiés ({max(change_percentages):.1f}% max) - transformation locale et ciblée"
                )
        
        # 6. Analyse des motifs
        if "patterns" in raw_analysis and "motif" in raw_analysis["patterns"]:
            motif = raw_analysis["patterns"]["motif"]
            if motif.get("detected", False):
                insights["critical_observations"].append(
                    f"MOTIF: Type '{motif.get('type', 'unknown')}' détecté avec couleurs {motif.get('motif_colors', [])}"
                )
        
        # 7. Analyse des patterns de transformation (générique)
        if insights["color_transformations"].get("total_transformations", 0) > 0:
            # Analyser si les transformations suivent un pattern spatial
            if "spatial_relations" in raw_analysis and insights["object_structure"].get("size_variety", 0) > 1:
                insights["critical_observations"].append(
                    "PATTERN: Transformations de couleur avec objets de tailles variées - possibles règles spatiales"
                )
        
    except Exception as e:
        insights["error"] = f"Erreur lors de l'extraction: {str(e)}"
    
    return insights

def format_insights_for_prompt(insights):
    """Formate les insights pour inclusion dans un prompt"""
    prompt_section = "\n🔍 INSIGHTS CRITIQUES (basés sur l'analyse automatique):\n"
    
    # Observations critiques
    if insights.get("critical_observations"):
        for obs in insights["critical_observations"]:
            prompt_section += f"• {obs}\n"
    
    # Transformations de couleur
    if insights.get("color_transformations", {}).get("changes"):
        changes = insights["color_transformations"]["changes"]
        prompt_section += f"\n• CHANGEMENTS DE COULEUR:\n"
        for change, count in sorted(changes.items(), key=lambda x: x[1], reverse=True):
            prompt_section += f"  - {change}: {count} occurrences\n"
    
    # Structure des objets
    if insights.get("object_structure"):
        struct = insights["object_structure"]
        if struct.get("most_common_sizes"):
            prompt_section += f"• TAILLES D'OBJETS: "
            for size, count in struct["most_common_sizes"]:
                prompt_section += f"taille {size} ({count}x), "
            prompt_section = prompt_section.rstrip(", ") + "\n"
    
    # Statistiques de changement
    if insights.get("change_statistics", {}).get("low_change_rate"):
        stats = insights["change_statistics"]
        prompt_section += f"• IMPACT: Seulement {stats['average_change_percentage']:.1f}% des pixels modifiés en moyenne\n"
    
    # Conseils génériques basés sur les données analysées
    if insights.get("critical_observations"):
        prompt_section += "\n💡 APPROCHE RECOMMANDÉE:\n"
        
        # Conseils basés sur le type de transformation détecté
        if any("TRANSFORMATION: Suppression de couleur" in obs for obs in insights["critical_observations"]):
            prompt_section += "• Se concentrer sur les transformations de couleur plutôt que les déplacements\n"
        
        if any("STRUCTURE:" in obs for obs in insights["critical_observations"]):
            prompt_section += "• Analyser la relation entre objets de différentes tailles\n"
        
        if insights.get("change_statistics", {}).get("low_change_rate"):
            prompt_section += "• Chercher des transformations locales et ciblées\n"
        
        prompt_section += "• Tester systématiquement l'hypothèse sur tous les exemples\n\n"
    
    return prompt_section

def main():
    """Fonction principale pour tester l'extraction"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Extracteur d\'insights ARC AGI')
    parser.add_argument('--analysis-file', type=str, required=True, 
                        help='Chemin vers le fichier d\'analyse JSON')
    parser.add_argument('--output', type=str, help='Fichier de sortie (optionnel)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.analysis_file):
        print(f"Erreur: Fichier non trouvé: {args.analysis_file}")
        return
    
    # Charger l'analyse
    with open(args.analysis_file, 'r') as f:
        analysis_data = json.load(f)
    
    # Extraire les insights
    insights = extract_key_insights(analysis_data)
    
    # Formater pour le prompt
    prompt_section = format_insights_for_prompt(insights)
    
    # Afficher ou sauvegarder
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(prompt_section)
        print(f"Insights sauvegardés dans {args.output}")
    else:
        print(prompt_section)
    
    # Afficher aussi les insights bruts
    print("\n" + "="*60)
    print("INSIGHTS BRUTS (pour debug):")
    print(json.dumps(insights, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    main()