#!/usr/bin/env python3
"""
Tests de validation finale du système complet
Valide le workflow complet sur des puzzles réels et l'apprentissage sans contamination
"""

import unittest
import tempfile
import json
import numpy as np
from pathlib import Path
from unittest.mock import patch, MagicMock

from scripts.core.automated_analyzer import AutomatedARCAnalyzer
from scripts.core.exceptions import PuzzleLoadingError, ChatSessionError
from scripts.core.data_models import AnalysisResult, ValidationResult, ParsedResponse


class TestFinalSystemValidation(unittest.TestCase):
    """Tests de validation finale du système complet"""
    
    def setUp(self):
        """Configuration des tests"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = {
            'arc_data_dir': self.temp_dir,
            'analysis_dir': f"{self.temp_dir}/analysis",
            'results_dir': f"{self.temp_dir}/results",
            'chat_session_type': 'blind_analysis',
            'enable_learning': True,
            'save_success': True
        }
        
        # Créer la structure de répertoires
        Path(f"{self.temp_dir}/training").mkdir(parents=True, exist_ok=True)
        Path(f"{self.temp_dir}/evaluation").mkdir(parents=True, exist_ok=True)
        Path(self.config['analysis_dir']).mkdir(parents=True, exist_ok=True)
        Path(self.config['results_dir']).mkdir(parents=True, exist_ok=True)
        
        # Créer un puzzle de test réaliste
        self.test_puzzle = {
            "train": [
                {
                    "input": [[1, 0, 1], [0, 1, 0], [1, 0, 1]],
                    "output": [[2, 0, 2], [0, 2, 0], [2, 0, 2]]
                }
            ],
            "test": [
                {
                    "input": [[1, 1, 0], [1, 0, 1], [0, 1, 1]],
                    "output": [[2, 2, 0], [2, 0, 2], [0, 2, 2]]
                }
            ]
        }
        
        # Sauvegarder le puzzle de test
        with open(f"{self.temp_dir}/training/test_real_001.json", 'w') as f:
            json.dump(self.test_puzzle, f)
    
    def test_complete_workflow_on_real_puzzle_structure(self):
        """Test du workflow complet sur une structure de puzzle réaliste"""
        with patch('scripts.core.chat_session.ChatSession') as mock_chat_class:
            # Configuration du mock de session chat
            mock_chat = MagicMock()
            mock_chat_class.return_value = mock_chat
            mock_chat.send_prompt.return_value = """
            TRANSFORMATIONS DÉTECTÉES:
            - Remplacement de couleur: 1 → 2
            
            PATTERNS IDENTIFIÉS:
            - Conservation de la structure
            - Transformation sélective des pixels
            
            RÈGLES DÉDUITES:
            - Si input[i][j] = 1 alors output[i][j] = 2
            - Si input[i][j] = 0 alors output[i][j] = 0
            
            GRILLE OUTPUT PROPOSÉE:
            [[2, 2, 0],
             [2, 0, 2],
             [0, 2, 2]]
            """
            
            # Créer l'analyseur
            analyzer = AutomatedARCAnalyzer(self.config)
            
            # Exécuter l'analyse
            result = analyzer.analyze_puzzle("test_real_001")
            
            # Vérifications
            self.assertIsNotNone(result)
            self.assertEqual(result.puzzle_id, "test_real_001")
            self.assertTrue(result.success)
            self.assertIsNotNone(result.proposed_solution)
            self.assertIsNotNone(result.validation_result)
            self.assertTrue(result.validation_result.is_correct)
            self.assertEqual(result.validation_result.accuracy_percentage, 100.0)
    
    def test_learning_system_without_contamination(self):
        """Test que l'apprentissage fonctionne sans contamination croisée"""
        with patch('scripts.core.chat_session.ChatSession') as mock_chat_class:
            mock_chat = MagicMock()
            mock_chat_class.return_value = mock_chat
            
            # Créer deux puzzles différents
            puzzle1_response = """
            TRANSFORMATIONS: Remplacement 1→2
            PATTERNS: Structure conservée
            RÈGLES: Transformation sélective
            GRILLE: [[2, 2, 0], [2, 0, 2], [0, 2, 2]]
            """
            
            puzzle2_response = """
            TRANSFORMATIONS: Rotation 90°
            PATTERNS: Géométrie modifiée
            RÈGLES: Rotation horaire
            GRILLE: [[0, 1, 1], [2, 0, 1], [2, 2, 0]]
            """
            
            mock_chat.send_prompt.side_effect = [puzzle1_response, puzzle2_response]
            
            # Créer un deuxième puzzle
            puzzle2 = {
                "train": [{"input": [[1, 1], [0, 2]], "output": [[0, 1], [2, 1]]}],
                "test": [{"input": [[1, 2], [2, 0]], "output": [[0, 1], [2, 1]]}]
            }
            
            with open(f"{self.temp_dir}/training/test_real_002.json", 'w') as f:
                json.dump(puzzle2, f)
            
            analyzer = AutomatedARCAnalyzer(self.config)
            
            # Analyser les deux puzzles
            result1 = analyzer.analyze_puzzle("test_real_001")
            result2 = analyzer.analyze_puzzle("test_real_002")
            
            # Vérifier qu'il n'y a pas de contamination
            self.assertNotEqual(result1.ai_analysis.transformations, 
                              result2.ai_analysis.transformations)
            self.assertNotEqual(result1.ai_analysis.patterns, 
                              result2.ai_analysis.patterns)
            self.assertNotEqual(result1.ai_analysis.rules, 
                              result2.ai_analysis.rules)
    
    def test_output_format_correctness(self):
        """Test que tous les formats de sortie sont corrects"""
        with patch('scripts.core.chat_session.ChatSession') as mock_chat_class:
            mock_chat = MagicMock()
            mock_chat_class.return_value = mock_chat
            mock_chat.send_prompt.return_value = """
            TRANSFORMATIONS DÉTECTÉES:
            - Test transformation
            
            PATTERNS IDENTIFIÉS:
            - Test pattern
            
            RÈGLES DÉDUITES:
            - Test rule
            
            GRILLE OUTPUT PROPOSÉE:
            [[2, 2, 0],
             [2, 0, 2],
             [0, 2, 2]]
            """
            
            analyzer = AutomatedARCAnalyzer(self.config)
            result = analyzer.analyze_puzzle("test_real_001")
            
            # Vérifier le format de sortie structuré
            formatted_output = analyzer.format_successful_analysis(result)
            
            # Vérifications du format
            self.assertIn("=== ANALYSE PUZZLE", formatted_output)
            self.assertIn("📊 MÉTADONNÉES", formatted_output)
            self.assertIn("🔍 TRANSFORMATIONS DÉTECTÉES", formatted_output)
            self.assertIn("🎯 PATTERNS IDENTIFIÉS", formatted_output)
            self.assertIn("📋 RÈGLES DÉDUITES", formatted_output)
            self.assertIn("🎲 GRILLE OUTPUT PROPOSÉE", formatted_output)
            self.assertIn("✅ VALIDATION", formatted_output)
            self.assertIn("SUCCÈS - Puzzle résolu !", formatted_output)
            
            # Vérifier que le fichier de résultat est créé
            expected_file = Path(self.config['results_dir']) / "puzzle_test_real_001_success.txt"
            self.assertTrue(expected_file.exists())
    
    def test_error_handling_robustness(self):
        """Test de la robustesse de la gestion d'erreurs"""
        # Test avec puzzle inexistant
        analyzer = AutomatedARCAnalyzer(self.config)
        
        with self.assertRaises(PuzzleLoadingError):
            analyzer.analyze_puzzle("nonexistent_puzzle")
        
        # Test avec erreur de session chat
        with patch('scripts.core.chat_session.ChatSession') as mock_chat_class:
            mock_chat = MagicMock()
            mock_chat_class.return_value = mock_chat
            mock_chat.send_prompt.side_effect = ChatSessionError("Connection failed")
            
            with self.assertRaises(ChatSessionError):
                analyzer.analyze_puzzle("test_real_001")
    
    def test_performance_on_multiple_puzzles(self):
        """Test des performances sur plusieurs puzzles"""
        # Créer plusieurs puzzles de test
        for i in range(3, 6):  # test_real_003 à test_real_005
            puzzle = {
                "train": [{"input": [[i, 0]], "output": [[i+1, 0]]}],
                "test": [{"input": [[i, 1]], "output": [[i+1, 1]]}]
            }
            with open(f"{self.temp_dir}/training/test_real_00{i}.json", 'w') as f:
                json.dump(puzzle, f)
        
        with patch('scripts.core.chat_session.ChatSession') as mock_chat_class:
            mock_chat = MagicMock()
            mock_chat_class.return_value = mock_chat
            mock_chat.send_prompt.return_value = """
            TRANSFORMATIONS: Incrémentation
            PATTERNS: Séquentiel
            RÈGLES: +1 sur première valeur
            GRILLE: [[4, 1]]
            """
            
            analyzer = AutomatedARCAnalyzer(self.config)
            results = []
            
            # Analyser tous les puzzles
            for i in range(3, 6):
                result = analyzer.analyze_puzzle(f"test_real_00{i}")
                results.append(result)
            
            # Vérifier que tous ont été traités
            self.assertEqual(len(results), 3)
            for result in results:
                self.assertIsNotNone(result)
                self.assertGreater(result.execution_time, 0)
    
    def test_cache_functionality(self):
        """Test de la fonctionnalité de cache des analyses"""
        with patch('scripts.core.chat_session.ChatSession') as mock_chat_class:
            mock_chat = MagicMock()
            mock_chat_class.return_value = mock_chat
            mock_chat.send_prompt.return_value = """
            TRANSFORMATIONS: Test cache
            PATTERNS: Cache pattern
            RÈGLES: Cache rule
            GRILLE: [[2, 2, 0], [2, 0, 2], [0, 2, 2]]
            """
            
            analyzer = AutomatedARCAnalyzer(self.config)
            
            # Première analyse
            result1 = analyzer.analyze_puzzle("test_real_001")
            call_count_1 = mock_chat.send_prompt.call_count
            
            # Deuxième analyse du même puzzle (devrait utiliser le cache)
            result2 = analyzer.analyze_puzzle("test_real_001")
            call_count_2 = mock_chat.send_prompt.call_count
            
            # Vérifier que le cache fonctionne
            self.assertEqual(result1.puzzle_id, result2.puzzle_id)
            # Le nombre d'appels ne devrait pas augmenter si le cache fonctionne
            # (Note: selon l'implémentation, cela peut varier)


class TestSystemIntegrationScenarios(unittest.TestCase):
    """Tests de scénarios d'intégration système"""
    
    def setUp(self):
        """Configuration des tests d'intégration"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = {
            'arc_data_dir': self.temp_dir,
            'analysis_dir': f"{self.temp_dir}/analysis",
            'results_dir': f"{self.temp_dir}/results",
            'chat_session_type': 'blind_analysis',
            'enable_learning': True,
            'save_success': True
        }
        
        # Créer la structure
        Path(f"{self.temp_dir}/training").mkdir(parents=True, exist_ok=True)
        Path(self.config['analysis_dir']).mkdir(parents=True, exist_ok=True)
        Path(self.config['results_dir']).mkdir(parents=True, exist_ok=True)
    
    def test_end_to_end_workflow_validation(self):
        """Test de validation du workflow de bout en bout"""
        # Créer un puzzle complexe
        complex_puzzle = {
            "train": [
                {"input": [[1, 2, 1], [2, 1, 2], [1, 2, 1]], 
                 "output": [[3, 4, 3], [4, 3, 4], [3, 4, 3]]},
                {"input": [[2, 1, 2], [1, 2, 1], [2, 1, 2]], 
                 "output": [[4, 3, 4], [3, 4, 3], [4, 3, 4]]}
            ],
            "test": [
                {"input": [[1, 1, 2], [1, 2, 1], [2, 1, 1]], 
                 "output": [[3, 3, 4], [3, 4, 3], [4, 3, 3]]}
            ]
        }
        
        with open(f"{self.temp_dir}/training/complex_001.json", 'w') as f:
            json.dump(complex_puzzle, f)
        
        with patch('scripts.core.chat_session.ChatSession') as mock_chat_class:
            mock_chat = MagicMock()
            mock_chat_class.return_value = mock_chat
            mock_chat.send_prompt.return_value = """
            TRANSFORMATIONS DÉTECTÉES:
            - Remplacement de couleur: 1 → 3, 2 → 4
            
            PATTERNS IDENTIFIÉS:
            - Conservation de la structure spatiale
            - Transformation systématique des couleurs
            
            RÈGLES DÉDUITES:
            - Si input[i][j] = 1 alors output[i][j] = 3
            - Si input[i][j] = 2 alors output[i][j] = 4
            
            GRILLE OUTPUT PROPOSÉE:
            [[3, 3, 4],
             [3, 4, 3],
             [4, 3, 3]]
            """
            
            analyzer = AutomatedARCAnalyzer(self.config)
            result = analyzer.analyze_puzzle("complex_001")
            
            # Vérifications complètes
            self.assertTrue(result.success)
            self.assertEqual(result.validation_result.accuracy_percentage, 100.0)
            self.assertIn("Remplacement de couleur", result.ai_analysis.transformations[0])
            self.assertIn("Conservation de la structure", result.ai_analysis.patterns[0])
            
            # Vérifier que le fichier de résultat contient toutes les sections
            result_file = Path(self.config['results_dir']) / "puzzle_complex_001_success.txt"
            self.assertTrue(result_file.exists())
            
            content = result_file.read_text(encoding='utf-8')
            required_sections = [
                "📊 MÉTADONNÉES", "🔍 TRANSFORMATIONS DÉTECTÉES",
                "🎯 PATTERNS IDENTIFIÉS", "📋 RÈGLES DÉDUITES",
                "🎲 GRILLE OUTPUT PROPOSÉE", "✅ VALIDATION"
            ]
            
            for section in required_sections:
                self.assertIn(section, content)


if __name__ == '__main__':
    unittest.main()