"""
Module core pour le système d'analyse automatisée ARC AGI.

Ce module contient les composants principaux du système d'analyse automatisée,
incluant les modèles de données, exceptions, et constantes.
"""

from .data_models import (
    AnalysisResult,
    ValidationResult,
    ParsedResponse,
    PuzzleData,
    LearningInsight
)

from .exceptions import (
    ARCAnalysisError,
    PuzzleLoadingError,
    PuzzleNotFoundError,
    InvalidPuzzleFormatError,
    ChatSessionError,
    ChatConnectionError,
    ChatTimeoutError,
    ResponseParsingError,
    GridExtractionError,
    SolutionValidationError,
    DimensionMismatchError,
    LearningSystemError,
    InsightExtractionError,
    GenericityValidationError,
    CrossContaminationError,
    FileOrganizationError,
    ConfigurationError
)

from .constants import (
    ExitCodes,
    AnalysisStatus,
    PuzzleSubsets,
    ChatSessionTypes,
    FileExtensions,
    DirectoryPaths,
    FilePatterns,
    ValidationThresholds,
    SystemLimits,
    DefaultConfigurations,
    ColorCodes,
    LoggingLevels,
    MessageTemplates,
    RegexPatterns
)

__all__ = [
    # Data models
    'AnalysisResult',
    'ValidationResult', 
    'ParsedResponse',
    'PuzzleData',
    'LearningInsight',
    
    # Exceptions
    'ARCAnalysisError',
    'PuzzleLoadingError',
    'PuzzleNotFoundError',
    'InvalidPuzzleFormatError',
    'ChatSessionError',
    'ChatConnectionError',
    'ChatTimeoutError',
    'ResponseParsingError',
    'GridExtractionError',
    'SolutionValidationError',
    'DimensionMismatchError',
    'LearningSystemError',
    'InsightExtractionError',
    'GenericityValidationError',
    'CrossContaminationError',
    'FileOrganizationError',
    'ConfigurationError',
    
    # Constants
    'ExitCodes',
    'AnalysisStatus',
    'PuzzleSubsets',
    'ChatSessionTypes',
    'FileExtensions',
    'DirectoryPaths',
    'FilePatterns',
    'ValidationThresholds',
    'SystemLimits',
    'DefaultConfigurations',
    'ColorCodes',
    'LoggingLevels',
    'MessageTemplates',
    'RegexPatterns'
]