#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script principal pour le système de prompts ARC AGI améliorés
Point d'entrée unique pour toutes les fonctionnalités
"""

import sys
import os
import argparse
import subprocess
from pathlib import Path

# Ajouter le répertoire scripts au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'scripts'))

def main():
    parser = argparse.ArgumentParser(
        description='Système de prompts ARC AGI améliorés',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Commandes disponibles:
  generate    Générer un prompt amélioré pour un puzzle
  validate    Valider la généricité des insights
  test        Exécuter les tests du système
  
Exemples:
  python arc_enhanced_prompt.py generate --taskid 2204b7a8
  python arc_enhanced_prompt.py validate
  python arc_enhanced_prompt.py test
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Commandes disponibles')
    
    # Commande generate
    generate_parser = subparsers.add_parser('generate', help='Générer un prompt amélioré')
    generate_parser.add_argument('--taskid', type=str, required=True, help='ID du puzzle')
    generate_parser.add_argument('--subset', type=str, default='training', 
                                choices=['training', 'evaluation'], help='Sous-ensemble')
    generate_parser.add_argument('--show-insights-only', action='store_true',
                                help='Afficher seulement les insights')
    generate_parser.add_argument('--force-regenerate', action='store_true',
                                help='Forcer la régénération de l\'analyse')
    generate_parser.add_argument('--quiet', action='store_true', help='Mode silencieux')
    
    # Commande validate
    validate_parser = subparsers.add_parser('validate', help='Valider la généricité')
    
    # Commande test
    test_parser = subparsers.add_parser('test', help='Exécuter les tests')
    test_parser.add_argument('--complete', action='store_true', 
                            help='Exécuter tous les tests')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    # Exécuter la commande appropriée
    if args.command == 'generate':
        return run_generate(args)
    elif args.command == 'validate':
        return run_validate()
    elif args.command == 'test':
        return run_test(args)
    
    return 0

def run_generate(args):
    """Exécuter la génération de prompt"""
    cmd = ['python', 'scripts/core/generate_enhanced_prompt.py']
    
    cmd.extend(['--taskid', args.taskid])
    cmd.extend(['--subset', args.subset])
    
    # Ajuster les chemins pour qu'ils soient relatifs à la racine
    cmd.extend(['--arc-data-dir', '../arc-puzzle/arcdata/'])
    cmd.extend(['--analysis-dir', 'analysis_data'])
    cmd.extend(['--output-dir', 'arc_results'])
    
    if args.show_insights_only:
        cmd.append('--show-insights-only')
    if args.force_regenerate:
        cmd.append('--force-regenerate')
    if args.quiet:
        cmd.append('--quiet')
    
    try:
        return subprocess.run(cmd, check=True).returncode
    except subprocess.CalledProcessError as e:
        return e.returncode

def run_validate():
    """Exécuter la validation"""
    cmd = ['python', 'scripts/utils/validate_generic_insights.py']
    
    try:
        return subprocess.run(cmd, check=True).returncode
    except subprocess.CalledProcessError as e:
        return e.returncode

def run_test(args):
    """Exécuter les tests"""
    if args.complete:
        cmd = ['python', 'scripts/tests/test_complete_workflow.py']
    else:
        cmd = ['python', 'scripts/tests/test_enhanced_prompt.py']
    
    try:
        return subprocess.run(cmd, check=True).returncode
    except subprocess.CalledProcessError as e:
        return e.returncode

if __name__ == "__main__":
    sys.exit(main())