#!/usr/bin/env python3
"""
Tests d'intégration complets pour le workflow automatisé ARC AGI.

Ce module teste le workflow complet de l'ID puzzle à la validation,
incluant tous les composants intégrés et les scénarios de succès/échec.
"""

import unittest
import sys
import json
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock, mock_open
import numpy as np
from datetime import datetime

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from core.automated_analyzer import AutomatedARCAnalyzer
from core.data_models import AnalysisResult, ValidationResult, ParsedResponse, PuzzleData
from core.constants import (
    ExitCodes, ChatSessionTypes, AnalysisStatus, ValidationThresholds,
    SystemLimits, MessageTemplates
)
from core.exceptions import (
    PuzzleNotFoundError, ChatSessionError, ResponseParsingError,
    SolutionValidationError, LearningSystemError
)


class TestCompleteAutomatedWorkflow(unittest.TestCase):
    """Tests d'intégration pour le workflow complet d'analyse automatisée."""
    
    def setUp(self):
        """Configuration des tests d'intégration."""
        # Créer un répertoire temporaire pour les tests
        self.temp_dir = tempfile.mkdtemp()
        self.temp_path = Path(self.temp_dir)
        
        # Configuration de test
        self.config = {
            'arc_data_dir': str(self.temp_path / 'arcdata'),
            'analysis_dir': str(self.temp_path / 'analysis'),
            'results_dir': str(self.temp_path / 'results'),
            'show_progress': False,
            'log_level': 'ERROR',
            'enable_learning': False,  # Désactiver pour les tests de base
            'save_success': True
        }
        
        # Créer les répertoires nécessaires
        (self.temp_path / 'arcdata' / 'training').mkdir(parents=True, exist_ok=True)
        (self.temp_path / 'arcdata' / 'evaluation').mkdir(parents=True, exist_ok=True)
        (self.temp_path / 'analysis' / 'training').mkdir(parents=True, exist_ok=True)
        (self.temp_path / 'results').mkdir(parents=True, exist_ok=True)
        
        # Créer des données de puzzle test
        self.test_puzzle_data = {
            "train": [
                {
                    "input": [[0, 1, 0], [1, 0, 1], [0, 1, 0]],
                    "output": [[1, 0, 1], [0, 1, 0], [1, 0, 1]]
                },
                {
                    "input": [[2, 3, 2], [3, 2, 3], [2, 3, 2]],
                    "output": [[3, 2, 3], [2, 3, 2], [3, 2, 3]]
                }
            ],
            "test": [
                {
                    "input": [[4, 5, 4], [5, 4, 5], [4, 5, 4]],
                    "output": [[5, 4, 5], [4, 5, 4], [5, 4, 5]]
                }
            ]
        }
        
        # Sauvegarder le puzzle test
        puzzle_file = self.temp_path / 'arcdata' / 'training' / 'test_001.json'
        with open(puzzle_file, 'w') as f:
            json.dump(self.test_puzzle_data, f)
    
    def tearDown(self):
        """Nettoyage après les tests."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @patch('core.automated_analyzer.ChatSession')
    @patch('core.automated_analyzer.extract_key_insights')
    def test_successful_complete_workflow(self, mock_extract_insights, mock_chat_class):
        """Test du workflow complet avec succès."""
        # Configuration des mocks
        mock_extract_insights.return_value = {
            'critical_observations': [
                'Transformation de couleur détectée',
                'Pattern d\'inversion identifié'
            ]
        }
        
        # Mock de la session chat
        mock_session = Mock()
        mock_chat_class.return_value.__enter__.return_value = mock_session
        
        # Réponse IA simulée avec solution correcte
        ai_response = """
        Analyse du puzzle test_001:
        
        TRANSFORMATIONS DÉTECTÉES:
        - Inversion des couleurs
        - Transformation pixel par pixel
        
        PATTERNS IDENTIFIÉS:
        - Chaque couleur devient son opposé
        - Structure spatiale préservée
        
        RÈGLES DÉDUITES:
        - Si input[i][j] = a alors output[i][j] = b où b est l'opposé de a
        - Maintenir la structure de la grille
        
        INTERPRÉTATION GRILLE TEST:
        La grille test suit le même pattern d'inversion des couleurs.
        
        GRILLE OUTPUT PROPOSÉE:
        [[5, 4, 5], [4, 5, 4], [5, 4, 5]]
        
        RAISONNEMENT ÉTAPE PAR ÉTAPE:
        1. Identifier le pattern d'inversion dans les exemples
        2. Appliquer la règle d'inversion à la grille test
        3. Vérifier la cohérence avec les exemples
        """
        
        mock_session.send_prompt.return_value = ai_response
        
        # Créer l'analyseur
        analyzer = AutomatedARCAnalyzer(self.config)
        
        # Exécuter l'analyse
        result = analyzer.analyze_puzzle("test_001")
        
        # Vérifications du résultat
        self.assertEqual(result.puzzle_id, "test_001")
        self.assertTrue(result.success)
        self.assertIsNotNone(result.proposed_solution)
        self.assertIsNotNone(result.validation_result)
        self.assertIsNotNone(result.ai_analysis)
        self.assertGreater(result.execution_time, 0)
        self.assertIsNotNone(result.saved_to)
        
        # Vérifier la validation
        self.assertTrue(result.validation_result.is_correct)
        self.assertEqual(result.validation_result.accuracy_percentage, 100.0)
        self.assertEqual(result.validation_result.total_errors, 0)
        
        # Vérifier l'analyse IA
        self.assertIn("Inversion des couleurs", result.ai_analysis.transformations)
        self.assertIn("Chaque couleur devient son opposé", result.ai_analysis.patterns)
        self.assertIn("Si input[i][j] = a alors output[i][j] = b", result.ai_analysis.rules)
        
        # Vérifier que la solution proposée est correcte
        expected_solution = np.array([[5, 4, 5], [4, 5, 4], [5, 4, 5]])
        np.testing.assert_array_equal(result.proposed_solution, expected_solution)
        
        # Vérifier que le fichier de résultat a été créé
        result_file = Path(result.saved_to)
        self.assertTrue(result_file.exists())
        
        # Vérifier le contenu du fichier de résultat
        with open(result_file, 'r', encoding='utf-8') as f:
            content = f.read()
            self.assertIn("=== ANALYSE PUZZLE test_001", content)
            self.assertIn("SUCCÈS - Puzzle résolu !", content)
            self.assertIn("Inversion des couleurs", content)
            self.assertIn("=== FIN ANALYSE ===", content)
    
    @patch('core.automated_analyzer.ChatSession')
    @patch('core.automated_analyzer.extract_key_insights')
    def test_failed_workflow_incorrect_solution(self, mock_extract_insights, mock_chat_class):
        """Test du workflow avec solution incorrecte."""
        # Configuration des mocks
        mock_extract_insights.return_value = {'critical_observations': []}
        
        # Mock de la session chat
        mock_session = Mock()
        mock_chat_class.return_value.__enter__.return_value = mock_session
        
        # Réponse IA simulée avec solution incorrecte
        ai_response = """
        Analyse du puzzle test_001:
        
        TRANSFORMATIONS DÉTECTÉES:
        - Rotation de 90 degrés
        
        PATTERNS IDENTIFIÉS:
        - Rotation horaire
        
        RÈGLES DÉDUITES:
        - Appliquer une rotation de 90 degrés
        
        GRILLE OUTPUT PROPOSÉE:
        [[4, 5, 4], [5, 4, 5], [4, 5, 4]]
        
        RAISONNEMENT ÉTAPE PAR ÉTAPE:
        1. Identifier la rotation dans les exemples
        2. Appliquer la rotation à la grille test
        """
        
        mock_session.send_prompt.return_value = ai_response
        
        # Créer l'analyseur
        analyzer = AutomatedARCAnalyzer(self.config)
        
        # Exécuter l'analyse
        result = analyzer.analyze_puzzle("test_001")
        
        # Vérifications du résultat
        self.assertEqual(result.puzzle_id, "test_001")
        self.assertFalse(result.success)
        self.assertIsNotNone(result.proposed_solution)
        self.assertIsNotNone(result.validation_result)
        self.assertIsNotNone(result.ai_analysis)
        
        # Vérifier la validation
        self.assertFalse(result.validation_result.is_correct)
        self.assertLess(result.validation_result.accuracy_percentage, 100.0)
        self.assertGreater(result.validation_result.total_errors, 0)
        
        # Vérifier que la grille de diagnostic est générée
        self.assertIsNotNone(result.validation_result.diagnostic_grid)
        
        # Vérifier qu'aucun fichier de succès n'a été créé
        self.assertIsNone(result.saved_to)
    
    @patch('core.automated_analyzer.ChatSession')
    def test_workflow_chat_session_error(self, mock_chat_class):
        """Test du workflow avec erreur de session chat."""
        # Mock de la session chat qui lève une exception
        mock_chat_class.side_effect = Exception("Erreur de connexion chat")
        
        # Créer l'analyseur
        analyzer = AutomatedARCAnalyzer(self.config)
        
        # Exécuter l'analyse
        result = analyzer.analyze_puzzle("test_001")
        
        # Vérifications du résultat d'erreur
        self.assertEqual(result.puzzle_id, "test_001")
        self.assertFalse(result.success)
        self.assertIsNone(result.proposed_solution)
        self.assertIsNone(result.validation_result)
        self.assertIsNone(result.ai_analysis)
        self.assertIsNotNone(result.error_message)
        self.assertIn("Erreur de connexion chat", result.error_message)
    
    def test_workflow_puzzle_not_found(self):
        """Test du workflow avec puzzle inexistant."""
        # Créer l'analyseur
        analyzer = AutomatedARCAnalyzer(self.config)
        
        # Exécuter l'analyse avec un puzzle inexistant
        result = analyzer.analyze_puzzle("nonexistent_puzzle")
        
        # Vérifications du résultat d'erreur
        self.assertEqual(result.puzzle_id, "nonexistent_puzzle")
        self.assertFalse(result.success)
        self.assertIsNone(result.proposed_solution)
        self.assertIsNone(result.validation_result)
        self.assertIsNone(result.ai_analysis)
        self.assertIsNotNone(result.error_message)
    
    @patch('core.automated_analyzer.ChatSession')
    @patch('core.automated_analyzer.extract_key_insights')
    def test_workflow_parsing_error(self, mock_extract_insights, mock_chat_class):
        """Test du workflow avec erreur de parsing."""
        # Configuration des mocks
        mock_extract_insights.return_value = {'critical_observations': []}
        
        # Mock de la session chat
        mock_session = Mock()
        mock_chat_class.return_value.__enter__.return_value = mock_session
        
        # Réponse IA malformée
        ai_response = "Réponse complètement malformée sans structure"
        mock_session.send_prompt.return_value = ai_response
        
        # Créer l'analyseur
        analyzer = AutomatedARCAnalyzer(self.config)
        
        # Exécuter l'analyse
        result = analyzer.analyze_puzzle("test_001")
        
        # Vérifications du résultat
        self.assertEqual(result.puzzle_id, "test_001")
        # Le parsing peut échouer mais l'analyse peut continuer avec des valeurs par défaut
        self.assertIsNotNone(result.ai_analysis)
    
    @patch('core.automated_analyzer.ChatSession')
    @patch('core.automated_analyzer.extract_key_insights')
    def test_workflow_with_learning_enabled(self, mock_extract_insights, mock_chat_class):
        """Test du workflow avec apprentissage activé."""
        # Activer l'apprentissage
        config_with_learning = self.config.copy()
        config_with_learning['enable_learning'] = True
        
        # Configuration des mocks
        mock_extract_insights.return_value = {'critical_observations': []}
        
        # Mock de la session chat
        mock_session = Mock()
        mock_chat_class.return_value.__enter__.return_value = mock_session
        
        # Réponse IA simulée avec solution correcte
        ai_response = """
        TRANSFORMATIONS DÉTECTÉES:
        - Inversion des couleurs
        
        GRILLE OUTPUT PROPOSÉE:
        [[5, 4, 5], [4, 5, 4], [5, 4, 5]]
        """
        
        mock_session.send_prompt.return_value = ai_response
        
        # Mock du système d'apprentissage
        with patch.object(AutomatedARCAnalyzer, '_conduct_learning_session') as mock_learning:
            # Créer l'analyseur
            analyzer = AutomatedARCAnalyzer(config_with_learning)
            
            # Exécuter l'analyse
            result = analyzer.analyze_puzzle("test_001")
            
            # Vérifier que l'apprentissage a été appelé si succès
            if result.success:
                mock_learning.assert_called_once()
            else:
                mock_learning.assert_not_called()
    
    @patch('core.automated_analyzer.ChatSession')
    @patch('core.automated_analyzer.extract_key_insights')
    def test_workflow_performance_metrics(self, mock_extract_insights, mock_chat_class):
        """Test des métriques de performance du workflow."""
        # Configuration des mocks
        mock_extract_insights.return_value = {'critical_observations': []}
        
        # Mock de la session chat
        mock_session = Mock()
        mock_chat_class.return_value.__enter__.return_value = mock_session
        mock_session.send_prompt.return_value = """
        GRILLE OUTPUT PROPOSÉE:
        [[5, 4, 5], [4, 5, 4], [5, 4, 5]]
        """
        
        # Créer l'analyseur
        analyzer = AutomatedARCAnalyzer(self.config)
        
        # Mesurer le temps d'exécution
        start_time = datetime.now()
        result = analyzer.analyze_puzzle("test_001")
        end_time = datetime.now()
        
        # Vérifications de performance
        self.assertGreater(result.execution_time, 0)
        self.assertLess(result.execution_time, 60)  # Moins d'une minute
        
        # Vérifier que le timestamp est cohérent
        self.assertGreaterEqual(result.timestamp, start_time)
        self.assertLessEqual(result.timestamp, end_time)
    
    @patch('core.automated_analyzer.ChatSession')
    @patch('core.automated_analyzer.extract_key_insights')
    def test_workflow_multiple_puzzles(self, mock_extract_insights, mock_chat_class):
        """Test du workflow sur plusieurs puzzles."""
        # Créer un deuxième puzzle
        puzzle_2_data = {
            "train": [{"input": [[1, 0]], "output": [[0, 1]]}],
            "test": [{"input": [[2, 3]], "output": [[3, 2]]}]
        }
        
        puzzle_2_file = self.temp_path / 'arcdata' / 'training' / 'test_002.json'
        with open(puzzle_2_file, 'w') as f:
            json.dump(puzzle_2_data, f)
        
        # Configuration des mocks
        mock_extract_insights.return_value = {'critical_observations': []}
        
        # Mock de la session chat
        mock_session = Mock()
        mock_chat_class.return_value.__enter__.return_value = mock_session
        
        def mock_response(prompt):
            if "test_001" in prompt:
                return "GRILLE OUTPUT PROPOSÉE:\n[[5, 4, 5], [4, 5, 4], [5, 4, 5]]"
            else:
                return "GRILLE OUTPUT PROPOSÉE:\n[[3, 2]]"
        
        mock_session.send_prompt.side_effect = mock_response
        
        # Créer l'analyseur
        analyzer = AutomatedARCAnalyzer(self.config)
        
        # Analyser les deux puzzles
        result_1 = analyzer.analyze_puzzle("test_001")
        result_2 = analyzer.analyze_puzzle("test_002")
        
        # Vérifications
        self.assertEqual(result_1.puzzle_id, "test_001")
        self.assertEqual(result_2.puzzle_id, "test_002")
        
        # Vérifier que les résultats sont indépendants
        self.assertNotEqual(result_1.timestamp, result_2.timestamp)
        
        # Vérifier que les deux analyses ont été effectuées
        self.assertEqual(mock_session.send_prompt.call_count, 2)
    
    def test_workflow_configuration_validation(self):
        """Test de la validation de configuration du workflow."""
        # Configuration invalide
        invalid_config = {
            'arc_data_dir': '/nonexistent/path',
            'show_progress': False,
            'log_level': 'ERROR'
        }
        
        # Créer l'analyseur avec configuration invalide
        analyzer = AutomatedARCAnalyzer(invalid_config)
        
        # L'analyseur doit être créé mais l'analyse peut échouer
        self.assertIsNotNone(analyzer)
        self.assertIsNotNone(analyzer.config)
    
    @patch('core.automated_analyzer.ChatSession')
    @patch('core.automated_analyzer.extract_key_insights')
    def test_workflow_edge_cases(self, mock_extract_insights, mock_chat_class):
        """Test des cas limites du workflow."""
        # Configuration des mocks
        mock_extract_insights.return_value = {'critical_observations': []}
        
        # Mock de la session chat
        mock_session = Mock()
        mock_chat_class.return_value.__enter__.return_value = mock_session
        
        # Test avec réponse vide
        mock_session.send_prompt.return_value = ""
        
        # Créer l'analyseur
        analyzer = AutomatedARCAnalyzer(self.config)
        
        # Exécuter l'analyse
        result = analyzer.analyze_puzzle("test_001")
        
        # Vérifier que l'analyse gère les cas limites
        self.assertEqual(result.puzzle_id, "test_001")
        self.assertIsNotNone(result.ai_analysis)
        
        # Test avec réponse très longue
        long_response = "A" * (SystemLimits.MAX_RESPONSE_LENGTH + 1000)
        mock_session.send_prompt.return_value = long_response
        
        result_long = analyzer.analyze_puzzle("test_001")
        self.assertEqual(result_long.puzzle_id, "test_001")


class TestWorkflowIntegrationScenarios(unittest.TestCase):
    """Tests de scénarios d'intégration spécifiques."""
    
    def setUp(self):
        """Configuration des tests de scénarios."""
        self.temp_dir = tempfile.mkdtemp()
        self.temp_path = Path(self.temp_dir)
        
        self.config = {
            'arc_data_dir': str(self.temp_path / 'arcdata'),
            'analysis_dir': str(self.temp_path / 'analysis'),
            'results_dir': str(self.temp_path / 'results'),
            'show_progress': False,
            'log_level': 'ERROR'
        }
        
        # Créer les répertoires
        (self.temp_path / 'arcdata' / 'training').mkdir(parents=True, exist_ok=True)
        (self.temp_path / 'results').mkdir(parents=True, exist_ok=True)
    
    def tearDown(self):
        """Nettoyage après les tests."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_scenario_complex_puzzle_analysis(self):
        """Test de scénario avec puzzle complexe."""
        # Créer un puzzle plus complexe
        complex_puzzle = {
            "train": [
                {
                    "input": [[0, 1, 2, 0], [1, 2, 0, 1], [2, 0, 1, 2], [0, 1, 2, 0]],
                    "output": [[2, 1, 0, 2], [1, 0, 2, 1], [0, 2, 1, 0], [2, 1, 0, 2]]
                }
            ],
            "test": [
                {
                    "input": [[3, 4, 5, 3], [4, 5, 3, 4], [5, 3, 4, 5], [3, 4, 5, 3]],
                    "output": [[5, 4, 3, 5], [4, 3, 5, 4], [3, 5, 4, 3], [5, 4, 3, 5]]
                }
            ]
        }
        
        puzzle_file = self.temp_path / 'arcdata' / 'training' / 'complex_001.json'
        with open(puzzle_file, 'w') as f:
            json.dump(complex_puzzle, f)
        
        # Mock des composants
        with patch('core.automated_analyzer.ChatSession') as mock_chat_class, \
             patch('core.automated_analyzer.extract_key_insights') as mock_extract:
            
            mock_extract.return_value = {'critical_observations': []}
            
            mock_session = Mock()
            mock_chat_class.return_value.__enter__.return_value = mock_session
            mock_session.send_prompt.return_value = """
            TRANSFORMATIONS DÉTECTÉES:
            - Inversion de couleurs complexe
            
            GRILLE OUTPUT PROPOSÉE:
            [[5, 4, 3, 5], [4, 3, 5, 4], [3, 5, 4, 3], [5, 4, 3, 5]]
            """
            
            # Créer l'analyseur et exécuter
            analyzer = AutomatedARCAnalyzer(self.config)
            result = analyzer.analyze_puzzle("complex_001")
            
            # Vérifications
            self.assertEqual(result.puzzle_id, "complex_001")
            self.assertIsNotNone(result.ai_analysis)
            
            # Vérifier que la grille proposée a la bonne taille
            if result.proposed_solution is not None:
                self.assertEqual(result.proposed_solution.shape, (4, 4))
    
    def test_scenario_retry_mechanism(self):
        """Test du mécanisme de retry en cas d'échec."""
        # Créer un puzzle simple
        simple_puzzle = {
            "train": [{"input": [[0]], "output": [[1]]}],
            "test": [{"input": [[2]], "output": [[3]]}]
        }
        
        puzzle_file = self.temp_path / 'arcdata' / 'training' / 'retry_001.json'
        with open(puzzle_file, 'w') as f:
            json.dump(simple_puzzle, f)
        
        # Mock avec échecs puis succès
        with patch('core.automated_analyzer.ChatSession') as mock_chat_class, \
             patch('core.automated_analyzer.extract_key_insights') as mock_extract:
            
            mock_extract.return_value = {'critical_observations': []}
            
            mock_session = Mock()
            mock_chat_class.return_value.__enter__.return_value = mock_session
            
            # Premier appel échoue, deuxième réussit
            mock_session.send_prompt.side_effect = [
                Exception("Timeout"),
                "GRILLE OUTPUT PROPOSÉE:\n[[3]]"
            ]
            
            # Configuration avec retry
            config_with_retry = self.config.copy()
            config_with_retry['max_retries'] = 2
            
            analyzer = AutomatedARCAnalyzer(config_with_retry)
            result = analyzer.analyze_puzzle("retry_001")
            
            # Le premier appel devrait échouer, mais l'analyse peut continuer
            self.assertEqual(result.puzzle_id, "retry_001")


def run_integration_tests():
    """Exécute tous les tests d'intégration."""
    # Créer la suite de tests
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Ajouter les tests
    suite.addTests(loader.loadTestsFromTestCase(TestCompleteAutomatedWorkflow))
    suite.addTests(loader.loadTestsFromTestCase(TestWorkflowIntegrationScenarios))
    
    # Exécuter les tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_integration_tests()
    sys.exit(0 if success else 1)