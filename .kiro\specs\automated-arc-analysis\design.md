# Document de Conception

## Vue d'ensemble

Le système d'analyse automatisée ARC AGI étend l'architecture existante pour fournir un workflow complet d'analyse, validation et amélioration continue. Il s'appuie sur les composants existants (`ARCAnalyzer`, `generate_enhanced_prompt.py`, `extract_key_insights.py`) tout en ajoutant de nouvelles capacités de validation automatique et d'apprentissage.

## Architecture

### Architecture Globale

```mermaid
graph TB
    A[Interface CLI] --> B[Orchestrateur Principal]
    B --> C[Chargeur de Puzzle]
    B --> D[Générateur de Prompt]
    B --> E[Session Chat]
    B --> F[Analyseur de Réponse]
    B --> G[Validateur de Solution]
    B --> H[Système d'Apprentissage]
    
    C --> I[ARCAnalyzer Existant]
    D --> J[extract_key_insights Existant]
    E --> K[API Chat Externe]
    F --> L[Parseur de Grille]
    G --> M[Comparateur Pixel]
    H --> N[Extracteur d'Insights]
    H --> O[Validateur de Généricité]
```

### Flux de Données

```mermaid
sequenceDiagram
    participant CLI as Interface CLI
    participant Orch as Orchestrateur
    participant Loader as Chargeur
    participant Gen as Générateur
    participant Chat as Session Chat
    participant Parser as Analyseur
    participant Valid as Validateur
    participant Learn as Apprentissage
    
    CLI->>Orch: puzzle_id
    Orch->>Loader: Charger puzzle
    Loader->>Orch: puzzle_data (sans solution)
    Orch->>Gen: Générer prompt amélioré
    Gen->>Orch: prompt_optimisé
    Orch->>Chat: Envoyer prompt
    Chat->>Orch: réponse_ia
    Orch->>Parser: Extraire solution
    Parser->>Orch: grille_proposée
    Orch->>Valid: Valider solution
    Valid->>Orch: résultat_validation
    alt Solution correcte
        Orch->>Learn: Proposer apprentissage
        Learn->>Orch: insights_améliorés
    end
    Orch->>CLI: résultats_finaux
```

## Composants et Interfaces

### 1. Interface CLI Étendue (`arc_enhanced_prompt.py`)

**Extension du point d'entrée existant** avec nouvelle commande `analyze`:

```python
# Nouvelle commande à ajouter
analyze_parser = subparsers.add_parser('analyze', help='Analyse automatisée complète')
analyze_parser.add_argument('--puzzle-id', type=str, required=True, help='ID du puzzle')
analyze_parser.add_argument('--chat-session', type=str, help='Type de session chat')
analyze_parser.add_argument('--save-success', action='store_true', help='Sauvegarder les succès')
analyze_parser.add_argument('--enable-learning', action='store_true', help='Activer l\'apprentissage')
```

### 2. Orchestrateur Principal (`scripts/core/automated_analyzer.py`)

**Nouveau composant central** qui coordonne tout le workflow:

```python
class AutomatedARCAnalyzer:
    def __init__(self):
        self.puzzle_loader = PuzzleLoader()
        self.prompt_generator = EnhancedPromptGenerator()
        self.chat_session = ChatSession()
        self.response_parser = ResponseParser()
        self.solution_validator = SolutionValidator()
        self.learning_system = LearningSystem()
    
    def analyze_puzzle(self, puzzle_id: str, options: Dict) -> AnalysisResult:
        """Workflow complet d'analyse automatisée"""
        pass
```

### 3. Chargeur de Puzzle Sécurisé (`scripts/core/secure_puzzle_loader.py`)

**Nouveau composant** qui charge les puzzles en cachant la solution:

```python
class SecurePuzzleLoader:
    def load_puzzle_for_analysis(self, puzzle_id: str) -> Tuple[Dict, Dict]:
        """
        Charge le puzzle en séparant données d'analyse et solution cachée
        Returns: (puzzle_data_for_analysis, hidden_solution)
        """
        pass
    
    def find_puzzle_file(self, puzzle_id: str) -> Tuple[Path, str]:
        """Trouve le fichier puzzle dans training ou evaluation"""
        pass
```

### 4. Session Chat (`scripts/core/chat_session.py`)

**Nouveau composant** pour gérer les interactions avec l'IA:

```python
class ChatSession:
    def __init__(self, session_type: str = "default"):
        self.session_type = session_type
        self.conversation_history = []
    
    def send_prompt(self, prompt: str) -> str:
        """Envoie le prompt et récupère la réponse complète"""
        pass
    
    def create_new_session(self) -> bool:
        """Crée une nouvelle session pour analyse 'à l'aveugle'"""
        pass
```

### 5. Analyseur de Réponse (`scripts/core/response_parser.py`)

**Nouveau composant** pour extraire les informations de la réponse IA:

```python
class ResponseParser:
    def parse_ai_response(self, response: str) -> ParsedResponse:
        """
        Parse la réponse de l'IA pour extraire:
        - Transformations détectées
        - Patterns identifiés
        - Règles déduites
        - Grille solution proposée
        - Raisonnement étape par étape
        """
        pass
    
    def extract_solution_grid(self, response: str) -> Optional[np.ndarray]:
        """Extrait la grille solution de la réponse"""
        pass
```

### 6. Validateur de Solution (`scripts/core/solution_validator.py`)

**Nouveau composant** pour validation automatique:

```python
class SolutionValidator:
    def validate_solution(self, proposed: np.ndarray, correct: np.ndarray) -> ValidationResult:
        """
        Compare pixel par pixel et génère grille T/F
        Returns: ValidationResult avec succès/échec et grille diagnostic
        """
        pass
    
    def generate_diagnostic_grid(self, proposed: np.ndarray, correct: np.ndarray) -> np.ndarray:
        """Génère grille avec T (correct) et F (incorrect)"""
        pass
```

### 7. Système d'Apprentissage (`scripts/core/learning_system.py`)

**Nouveau composant** pour amélioration continue:

```python
class LearningSystem:
    def __init__(self):
        self.insight_extractor = InsightExtractor()
        self.genericity_validator = GenericityValidator()
    
    def propose_learning(self, analysis_result: AnalysisResult) -> bool:
        """Demande à l'utilisateur s'il veut extraire des insights"""
        pass
    
    def extract_and_integrate_insights(self, successful_analysis: Dict) -> bool:
        """Extrait insights et les intègre après validation"""
        pass
```

## Modèles de Données

### AnalysisResult

```python
@dataclass
class AnalysisResult:
    puzzle_id: str
    success: bool
    proposed_solution: Optional[np.ndarray]
    validation_result: ValidationResult
    ai_analysis: ParsedResponse
    execution_time: float
    saved_to: Optional[str]
```

### ValidationResult

```python
@dataclass
class ValidationResult:
    is_correct: bool
    accuracy_percentage: float
    diagnostic_grid: np.ndarray  # Grille T/F
    total_errors: int
    error_positions: List[Tuple[int, int]]
```

### ParsedResponse

```python
@dataclass
class ParsedResponse:
    transformations: List[str]
    patterns: List[str]
    rules: List[str]
    proposed_grid: Optional[np.ndarray]
    reasoning_steps: List[str]
    interpretation: str
    raw_response: str
```

## Gestion des Erreurs

### Stratégie de Gestion d'Erreurs

1. **Erreurs de Chargement**: Fichier puzzle non trouvé
   - Message clair avec suggestions de vérification
   - Codes de retour appropriés

2. **Erreurs de Session Chat**: Problèmes de communication
   - Retry automatique avec backoff
   - Fallback vers mode dégradé

3. **Erreurs de Parsing**: Réponse IA non parsable
   - Tentatives multiples avec prompts clarifiés
   - Sauvegarde de la réponse brute pour debug

4. **Erreurs de Validation**: Problèmes de comparaison
   - Vérification des dimensions
   - Messages d'erreur détaillés

### Codes de Retour

```python
class ExitCodes:
    SUCCESS = 0
    PUZZLE_NOT_FOUND = 1
    ANALYSIS_GENERATION_FAILED = 2
    CHAT_SESSION_ERROR = 3
    PARSING_ERROR = 4
    VALIDATION_ERROR = 5
    LEARNING_ERROR = 6
```

## Stratégie de Test

### Tests Unitaires (`scripts/tests/`)

1. **test_secure_puzzle_loader.py**: Test du chargement sécurisé
2. **test_response_parser.py**: Test du parsing des réponses IA
3. **test_solution_validator.py**: Test de la validation pixel par pixel
4. **test_learning_system.py**: Test de l'extraction d'insights

### Tests d'Intégration

1. **test_complete_automated_workflow.py**: Test du workflow complet
2. **test_learning_integration.py**: Test de l'apprentissage bout en bout

### Tests de Conformité

1. **test_file_organization_compliance.py**: Vérification des règles d'organisation
2. **test_insight_genericity.py**: Vérification de la généricité des insights

## Format de Sortie Structuré

### Format Texte de Sauvegarde

```
=== ANALYSE PUZZLE [ID] - [TIMESTAMP] ===

📊 MÉTADONNÉES
Puzzle ID: [ID]
Subset: [training/evaluation]
Durée d'analyse: [X.X]s
Statut: [SUCCÈS/ÉCHEC]

🔍 TRANSFORMATIONS DÉTECTÉES
- [Liste des transformations identifiées par l'IA]

🎯 PATTERNS IDENTIFIÉS
- [Patterns trouvés dans les exemples]

📋 RÈGLES DÉDUITES
- [Règles logiques extraites]

💡 INTERPRÉTATION GRILLE TEST
[Description de l'interprétation de la grille test]

🎲 GRILLE OUTPUT PROPOSÉE
[Grille solution au format visuel avec couleurs]

🧠 RAISONNEMENT ÉTAPE PAR ÉTAPE
1. [Étape 1 du raisonnement]
2. [Étape 2 du raisonnement]
...

✅ VALIDATION
[Soit "SUCCÈS - Puzzle résolu !" soit grille T/F pour diagnostic]

🎯 INSIGHTS EXTRAITS (si apprentissage activé)
[Nouveaux insights intégrés au système]

=== FIN ANALYSE ===
```

## Intégration avec l'Architecture Existante

### Réutilisation des Composants

1. **ARCAnalyzer**: Utilisé tel quel pour l'analyse de base
2. **generate_enhanced_prompt.py**: Étendu pour supporter le nouveau workflow
3. **extract_key_insights.py**: Intégré dans le système d'apprentissage
4. **validate_generic_insights.py**: Utilisé pour valider les nouveaux insights

### Extensions Nécessaires

1. **Point d'entrée**: Ajout de la commande `analyze` dans `arc_enhanced_prompt.py`
2. **Nouveaux modules**: Création des composants dans `scripts/core/`
3. **Tests**: Ajout des tests dans `scripts/tests/`

### Respect des Règles de Steering

1. **file_organization_rules**: Tous les nouveaux fichiers dans les bons dossiers
2. **arc_insights_safety**: Validation stricte de la généricité des insights
3. **documentation_policy**: Pas de documentation automatique non demandée

## Sécurité et Performance

### Sécurité

1. **Isolation des solutions**: Les solutions correctes restent cachées pendant l'analyse
2. **Validation des insights**: Vérification automatique de la généricité
3. **Gestion des erreurs**: Pas de fuite d'informations sensibles

### Performance

1. **Cache des analyses**: Réutilisation des analyses existantes
2. **Parsing optimisé**: Extraction efficace des grilles depuis les réponses
3. **Validation rapide**: Comparaison vectorisée des grilles

### Évolutivité

1. **Architecture modulaire**: Composants indépendants et testables
2. **Interface extensible**: Facile d'ajouter de nouveaux types de sessions chat
3. **Système d'apprentissage**: Amélioration continue des capacités