"""
Validateur de solution pour le système d'analyse automatisée ARC AGI.

Ce module implémente la validation automatique des solutions proposées
contre les solutions correctes, avec génération de grilles de diagnostic
et calcul de métriques de précision.
"""

import numpy as np
from dataclasses import dataclass
from typing import List, Tuple, Optional, Dict, Any
import logging
from pathlib import Path

from .constants import (
    ExitCodes, ValidationThresholds, SystemLimits, 
    ColorCodes, LoggingLevels
)


@dataclass
class ValidationResult:
    """Résultat de la validation d'une solution."""
    is_correct: bool
    accuracy_percentage: float
    diagnostic_grid: np.ndarray  # Grille T/F
    total_errors: int
    error_positions: List[Tuple[int, int]]
    correct_pixels: int
    total_pixels: int
    error_statistics: Dict[str, Any]


class SolutionValidationError(Exception):
    """Exception levée lors d'erreurs de validation."""
    pass


class SolutionValidator:
    """
    Validateur de solution pour les puzzles ARC AGI.
    
    Cette classe compare les solutions proposées avec les solutions correctes,
    génère des grilles de diagnostic et calcule des métriques de précision.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialise le validateur de solution.
        
        Args:
            config: Configuration optionnelle pour le validateur
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Configuration par défaut
        self.generate_diagnostic = self.config.get('generate_diagnostic', True)
        self.calculate_statistics = self.config.get('calculate_statistics', True)
        self.save_results = self.config.get('save_results', True)
    
    def validate_solution(self, proposed: np.ndarray, correct: np.ndarray) -> ValidationResult:
        """
        Compare pixel par pixel une solution proposée avec la solution correcte.
        
        Args:
            proposed: Grille solution proposée par l'IA
            correct: Grille solution correcte
            
        Returns:
            ValidationResult avec tous les détails de la validation
            
        Raises:
            SolutionValidationError: Si les grilles ne peuvent pas être comparées
        """
        try:
            # Validation des entrées
            self._validate_input_grids(proposed, correct)
            
            # Comparaison pixel par pixel
            comparison_result = self._compare_pixels(proposed, correct)
            
            # Génération de la grille de diagnostic
            diagnostic_grid = None
            if self.generate_diagnostic:
                diagnostic_grid = self.generate_diagnostic_grid(proposed, correct)
            
            # Calcul des statistiques d'erreur
            error_statistics = {}
            if self.calculate_statistics:
                error_statistics = self._calculate_error_statistics(
                    proposed, correct, comparison_result['error_positions']
                )
            
            # Construction du résultat
            validation_result = ValidationResult(
                is_correct=comparison_result['is_correct'],
                accuracy_percentage=comparison_result['accuracy_percentage'],
                diagnostic_grid=diagnostic_grid,
                total_errors=comparison_result['total_errors'],
                error_positions=comparison_result['error_positions'],
                correct_pixels=comparison_result['correct_pixels'],
                total_pixels=comparison_result['total_pixels'],
                error_statistics=error_statistics
            )
            
            self.logger.info(
                f"Validation terminée: {validation_result.accuracy_percentage:.1f}% précision, "
                f"{validation_result.total_errors} erreurs"
            )
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la validation: {str(e)}")
            raise SolutionValidationError(f"Échec de la validation: {str(e)}")
    
    def generate_diagnostic_grid(self, proposed: np.ndarray, correct: np.ndarray) -> np.ndarray:
        """
        Génère une grille de diagnostic avec 'T' pour correct et 'F' pour incorrect.
        
        Args:
            proposed: Grille solution proposée
            correct: Grille solution correcte
            
        Returns:
            Grille numpy avec 'T' et 'F' comme chaînes de caractères
            
        Raises:
            SolutionValidationError: Si les grilles ne peuvent pas être comparées
        """
        try:
            # Validation des entrées
            self._validate_input_grids(proposed, correct)
            
            # Création de la grille de diagnostic
            diagnostic = np.where(proposed == correct, 'T', 'F')
            
            self.logger.debug(f"Grille de diagnostic générée: {diagnostic.shape}")
            
            return diagnostic
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la génération de la grille de diagnostic: {str(e)}")
            raise SolutionValidationError(f"Échec de génération de diagnostic: {str(e)}")
    
    def _validate_input_grids(self, proposed: np.ndarray, correct: np.ndarray) -> None:
        """
        Valide que les grilles d'entrée sont compatibles pour la comparaison.
        
        Args:
            proposed: Grille proposée
            correct: Grille correcte
            
        Raises:
            SolutionValidationError: Si les grilles ne sont pas compatibles
        """
        # Vérifier que les deux grilles sont des numpy arrays
        if not isinstance(proposed, np.ndarray) or not isinstance(correct, np.ndarray):
            raise SolutionValidationError("Les grilles doivent être des numpy arrays")
        
        # Vérifier que les dimensions correspondent
        if proposed.shape != correct.shape:
            raise SolutionValidationError(
                f"Dimensions incompatibles: proposée {proposed.shape} vs correcte {correct.shape}"
            )
        
        # Vérifier que les grilles ne sont pas vides
        if proposed.size == 0 or correct.size == 0:
            raise SolutionValidationError("Les grilles ne peuvent pas être vides")
        
        # Vérifier les limites de taille
        height, width = proposed.shape
        if (height > SystemLimits.MAX_GRID_SIZE or width > SystemLimits.MAX_GRID_SIZE or
            height < SystemLimits.MIN_GRID_SIZE or width < SystemLimits.MIN_GRID_SIZE):
            raise SolutionValidationError(
                f"Taille de grille invalide: {height}x{width}. "
                f"Doit être entre {SystemLimits.MIN_GRID_SIZE} et {SystemLimits.MAX_GRID_SIZE}"
            )
        
        # Vérifier que les valeurs sont dans la plage des couleurs ARC
        for grid, name in [(proposed, "proposée"), (correct, "correcte")]:
            if not np.all(np.isin(grid, ColorCodes.ALL_COLORS)):
                invalid_values = np.unique(grid[~np.isin(grid, ColorCodes.ALL_COLORS)])
                raise SolutionValidationError(
                    f"Grille {name} contient des valeurs invalides: {invalid_values}. "
                    f"Valeurs autorisées: {ColorCodes.ALL_COLORS}"
                )
    
    def _compare_pixels(self, proposed: np.ndarray, correct: np.ndarray) -> Dict[str, Any]:
        """
        Compare les pixels des deux grilles et calcule les métriques de base.
        
        Args:
            proposed: Grille proposée
            correct: Grille correcte
            
        Returns:
            Dictionnaire avec les résultats de la comparaison
        """
        # Comparaison pixel par pixel
        matches = (proposed == correct)
        
        # Calcul des métriques
        total_pixels = proposed.size
        correct_pixels = np.sum(matches)
        total_errors = total_pixels - correct_pixels
        accuracy_percentage = (correct_pixels / total_pixels) * 100.0
        
        # Positions des erreurs
        error_positions = []
        if total_errors > 0:
            error_indices = np.where(~matches)
            error_positions = list(zip(error_indices[0].tolist(), error_indices[1].tolist()))
        
        # Déterminer si la solution est correcte
        is_correct = (accuracy_percentage == ValidationThresholds.PERFECT_ACCURACY)
        
        return {
            'is_correct': is_correct,
            'accuracy_percentage': accuracy_percentage,
            'total_errors': total_errors,
            'error_positions': error_positions,
            'correct_pixels': correct_pixels,
            'total_pixels': total_pixels
        }
    
    def _calculate_error_statistics(self, proposed: np.ndarray, correct: np.ndarray, 
                                  error_positions: List[Tuple[int, int]]) -> Dict[str, Any]:
        """
        Calcule des statistiques détaillées sur les erreurs.
        
        Args:
            proposed: Grille proposée
            correct: Grille correcte
            error_positions: Liste des positions d'erreur
            
        Returns:
            Dictionnaire avec les statistiques d'erreur
        """
        statistics = {
            'error_count_by_color': {},
            'most_common_errors': [],
            'error_distribution': {},
            'accuracy_level': self._get_accuracy_level(
                len(error_positions), proposed.size
            )
        }
        
        if not error_positions:
            return statistics
        
        # Analyse des erreurs par couleur
        error_by_color = {}
        error_patterns = {}
        
        for row, col in error_positions:
            proposed_color = proposed[row, col]
            correct_color = correct[row, col]
            
            # Compter les erreurs par couleur proposée
            if proposed_color not in error_by_color:
                error_by_color[proposed_color] = 0
            error_by_color[proposed_color] += 1
            
            # Compter les patterns d'erreur (couleur_proposée -> couleur_correcte)
            error_pattern = f"{proposed_color}→{correct_color}"
            if error_pattern not in error_patterns:
                error_patterns[error_pattern] = 0
            error_patterns[error_pattern] += 1
        
        # Trier par fréquence
        statistics['error_count_by_color'] = dict(
            sorted(error_by_color.items(), key=lambda x: x[1], reverse=True)
        )
        
        statistics['most_common_errors'] = sorted(
            error_patterns.items(), key=lambda x: x[1], reverse=True
        )[:5]  # Top 5 des erreurs les plus fréquentes
        
        # Distribution spatiale des erreurs
        height, width = proposed.shape
        quadrants = {
            'top_left': 0, 'top_right': 0,
            'bottom_left': 0, 'bottom_right': 0
        }
        
        mid_row, mid_col = height // 2, width // 2
        
        for row, col in error_positions:
            if row < mid_row and col < mid_col:
                quadrants['top_left'] += 1
            elif row < mid_row and col >= mid_col:
                quadrants['top_right'] += 1
            elif row >= mid_row and col < mid_col:
                quadrants['bottom_left'] += 1
            else:
                quadrants['bottom_right'] += 1
        
        statistics['error_distribution'] = quadrants
        
        return statistics
    
    def _get_accuracy_level(self, error_count: int, total_pixels: int) -> str:
        """
        Détermine le niveau de précision basé sur le pourcentage d'erreurs.
        
        Args:
            error_count: Nombre d'erreurs
            total_pixels: Nombre total de pixels
            
        Returns:
            Niveau de précision sous forme de chaîne
        """
        accuracy = ((total_pixels - error_count) / total_pixels) * 100.0
        
        if accuracy == ValidationThresholds.PERFECT_ACCURACY:
            return "PARFAIT"
        elif accuracy >= ValidationThresholds.HIGH_ACCURACY:
            return "ÉLEVÉ"
        elif accuracy >= ValidationThresholds.MEDIUM_ACCURACY:
            return "MOYEN"
        elif accuracy >= ValidationThresholds.LOW_ACCURACY:
            return "FAIBLE"
        else:
            return "TRÈS_FAIBLE"
    
    def format_diagnostic_grid(self, diagnostic_grid: np.ndarray) -> str:
        """
        Formate la grille de diagnostic pour l'affichage.
        
        Args:
            diagnostic_grid: Grille de diagnostic avec 'T' et 'F'
            
        Returns:
            Chaîne formatée pour l'affichage
        """
        if diagnostic_grid is None:
            return "Grille de diagnostic non disponible"
        
        lines = []
        lines.append("Grille de diagnostic (T=correct, F=incorrect):")
        lines.append("=" * (diagnostic_grid.shape[1] * 2 + 1))
        
        for row in diagnostic_grid:
            line = " ".join(str(cell) for cell in row)
            lines.append(line)
        
        lines.append("=" * (diagnostic_grid.shape[1] * 2 + 1))
        
        return "\n".join(lines)
    
    def get_validation_summary(self, result: ValidationResult) -> str:
        """
        Génère un résumé textuel de la validation.
        
        Args:
            result: Résultat de la validation
            
        Returns:
            Résumé formaté de la validation
        """
        lines = []
        
        # Statut principal
        if result.is_correct:
            lines.append("✅ SUCCÈS - Puzzle résolu !")
        else:
            lines.append("❌ ÉCHEC - Solution incorrecte")
        
        # Métriques de base
        lines.append(f"📊 Précision: {result.accuracy_percentage:.1f}%")
        lines.append(f"🎯 Pixels corrects: {result.correct_pixels}/{result.total_pixels}")
        
        if result.total_errors > 0:
            lines.append(f"❌ Erreurs: {result.total_errors}")
            lines.append(f"📈 Niveau de précision: {result.error_statistics.get('accuracy_level', 'N/A')}")
        
        return "\n".join(lines)