"""
Modèles de données pour le système d'analyse automatisée ARC AGI.

Ce module contient les dataclasses et structures de données utilisées
dans tout le workflow d'analyse automatisée.
"""

from dataclasses import dataclass
from typing import Optional, List, Tuple, Dict, Any
import numpy as np
from datetime import datetime


@dataclass
class ValidationResult:
    """Résultat de la validation d'une solution proposée."""
    is_correct: bool
    accuracy_percentage: float
    diagnostic_grid: np.ndarray  # Grille T/F pour diagnostic
    total_errors: int
    error_positions: List[Tuple[int, int]]
    
    def __post_init__(self):
        """Validation des données après initialisation."""
        if not 0 <= self.accuracy_percentage <= 100:
            raise ValueError("accuracy_percentage doit être entre 0 et 100")
        if self.total_errors < 0:
            raise ValueError("total_errors ne peut pas être négatif")


@dataclass
class ParsedResponse:
    """Réponse IA analysée et structurée."""
    transformations: List[str]
    patterns: List[str]
    rules: List[str]
    proposed_grid: Optional[np.ndarray]
    reasoning_steps: List[str]
    interpretation: str
    raw_response: str
    
    def __post_init__(self):
        """Validation des données après initialisation."""
        if not self.raw_response.strip():
            raise ValueError("raw_response ne peut pas être vide")


@dataclass
class AnalysisResult:
    """Résultat complet d'une analyse de puzzle."""
    puzzle_id: str
    success: bool
    proposed_solution: Optional[np.ndarray]
    validation_result: Optional[ValidationResult]
    ai_analysis: Optional[ParsedResponse]
    execution_time: float
    saved_to: Optional[str]
    timestamp: datetime
    error_message: Optional[str] = None
    
    def __post_init__(self):
        """Validation des données après initialisation."""
        if not self.puzzle_id.strip():
            raise ValueError("puzzle_id ne peut pas être vide")
        if self.execution_time < 0:
            raise ValueError("execution_time ne peut pas être négatif")
        if self.success and self.validation_result is None:
            raise ValueError("validation_result requis quand success=True")


@dataclass
class PuzzleData:
    """Données d'un puzzle chargé pour analyse."""
    puzzle_id: str
    train_examples: List[Dict[str, Any]]
    test_input: np.ndarray
    hidden_solution: Optional[np.ndarray]  # Cachée pendant l'analyse
    subset: str  # 'training' ou 'evaluation'
    
    def __post_init__(self):
        """Validation des données après initialisation."""
        if not self.puzzle_id.strip():
            raise ValueError("puzzle_id ne peut pas être vide")
        if self.subset not in ['training', 'evaluation']:
            raise ValueError("subset doit être 'training' ou 'evaluation'")
        if not self.train_examples:
            raise ValueError("train_examples ne peut pas être vide")


@dataclass
class LearningInsight:
    """Insight extrait pour l'apprentissage."""
    insight_type: str
    description: str
    conditions: List[str]
    confidence: float
    source_puzzle: str
    extracted_at: datetime
    
    def __post_init__(self):
        """Validation des données après initialisation."""
        if not 0 <= self.confidence <= 1:
            raise ValueError("confidence doit être entre 0 et 1")
        if not self.description.strip():
            raise ValueError("description ne peut pas être vide")