---
inclusion: always
priority: high
---

# 📁 RÈGLES STRICTES - Organisation des Fichiers ARC AGI

## ⚠️ PRINCIPE FONDAMENTAL - STRUCTURE ORGANISÉE

**RÈGLE ABSOLUE** : Le système ARC AGI amélioré doit maintenir une structure de fichiers **parfaitement organisée** et **logiquement cohérente**.

### 📂 STRUCTURE OBLIGATOIRE

```
arc-solver/
├── arc_enhanced_prompt.py          # 🎯 SEUL point d'entrée autorisé à la racine
├── scripts/                        # 📂 TOUT le code doit être ici
│   ├── core/                       # 🔧 Fonctionnalités principales UNIQUEMENT
│   │   ├── __init__.py
│   │   ├── arc_analyzer.py                 # Analyseur ARC
│   │   ├── generate_enhanced_prompt.py     # Orchestrateur principal
│   │   ├── generate_analysis.py            # Générateur d'analyses
│   │   └── arc_prompt_generator4.py        # Générateur de prompts
│   ├── utils/                      # 🛠️ Utilitaires UNIQUEMENT
│   │   ├── __init__.py
│   │   ├── extract_key_insights.py         # Extracteur d'insights
│   │   └── validate_generic_insights.py    # Validateur de généricité
│   └── tests/                      # 🧪 Tests UNIQUEMENT
│       ├── __init__.py
│       ├── test_complete_workflow.py       # Tests complets
│       └── test_enhanced_prompt.py         # Tests spécifiques
├── docs/                           # 📚 Documentation UNIQUEMENT
├── analysis_data/                  # 📊 Données générées (ne pas toucher)
├── arc_results/                    # 📝 Résultats générés (ne pas toucher)
├── .kiro/steering/                 # 🛡️ Règles (ne pas toucher)
├── cleanup_old_files.py            # 🧹 Utilitaire de nettoyage
└── README.md                       # 📖 Documentation principale
```

## 🚫 INTERDICTIONS STRICTES

### Fichiers INTERDITS à la Racine
- ❌ **AUCUN script Python** sauf `arc_enhanced_prompt.py`
- ❌ **AUCUN fichier de génération** (`generate_*.py`)
- ❌ **AUCUN fichier d'analyse** (`*_analysis.py`, `arc_analyzer.py`)
- ❌ **AUCUN fichier de test** (`test_*.py`)
- ❌ **AUCUN utilitaire** (`extract_*.py`, `validate_*.py`)
- ❌ **AUCUN fichier de documentation** (`README_*.md`, `SUMMARY_*.md`)

### Placement INTERDIT
- ❌ **Scripts core** dans `utils/` ou `tests/`
- ❌ **Utilitaires** dans `core/` ou `tests/`
- ❌ **Tests** dans `core/` ou `utils/`
- ❌ **Documentation** à la racine (sauf `README.md`)

## ✅ RÈGLES DE PLACEMENT OBLIGATOIRES

### 🔧 **scripts/core/** - Fonctionnalités Principales
**UNIQUEMENT** les scripts qui implémentent les fonctionnalités principales :
- ✅ `arc_analyzer.py` - Analyseur ARC AGI
- ✅ `generate_enhanced_prompt.py` - Orchestrateur principal
- ✅ `generate_analysis.py` - Générateur d'analyses
- ✅ `arc_prompt_generator4.py` - Générateur de prompts

### 🛠️ **scripts/utils/** - Utilitaires
**UNIQUEMENT** les outils d'aide et de validation :
- ✅ `extract_key_insights.py` - Extracteur d'insights
- ✅ `validate_generic_insights.py` - Validateur de généricité

### 🧪 **scripts/tests/** - Tests
**UNIQUEMENT** les scripts de test :
- ✅ `test_complete_workflow.py` - Tests complets
- ✅ `test_enhanced_prompt.py` - Tests spécifiques

### 📚 **docs/** - Documentation
**UNIQUEMENT** les fichiers de documentation :
- ✅ Tous les fichiers `.md` sauf `README.md` (qui reste à la racine)

## 🔍 VALIDATION OBLIGATOIRE

### Avant Chaque Commit/Modification
```bash
# OBLIGATOIRE : Vérifier la structure
ls -la  # Vérifier qu'il n'y a pas de scripts à la racine
ls scripts/core/  # Vérifier que les scripts core sont bien placés
ls scripts/utils/  # Vérifier que les utilitaires sont bien placés
ls scripts/tests/  # Vérifier que les tests sont bien placés
```

### Critères de Validation
- ✅ **Un seul script Python** à la racine : `arc_enhanced_prompt.py`
- ✅ **Tous les scripts core** dans `scripts/core/`
- ✅ **Tous les utilitaires** dans `scripts/utils/`
- ✅ **Tous les tests** dans `scripts/tests/`
- ✅ **Toute la documentation** dans `docs/`

## 🚨 CONSÉQUENCES DE LA VIOLATION

**IMPACT CATASTROPHIQUE** si ces règles sont violées :
- 🔥 **Désorganisation** : Perte de la structure logique
- 🔥 **Confusion** : Difficile de trouver les fichiers
- 🔥 **Maintenance impossible** : Code éparpillé partout
- 🔥 **Imports cassés** : Chemins relatifs incorrects
- 🔥 **Perte de productivité** : Temps perdu à chercher les fichiers

## 📋 CHECKLIST OBLIGATOIRE

Avant toute modification du système :

- [ ] **Vérifier** : Un seul script Python à la racine
- [ ] **Confirmer** : Tous les scripts core dans `scripts/core/`
- [ ] **Valider** : Tous les utilitaires dans `scripts/utils/`
- [ ] **S'assurer** : Tous les tests dans `scripts/tests/`
- [ ] **Contrôler** : Toute la documentation dans `docs/`
- [ ] **Tester** : `python arc_enhanced_prompt.py --help` fonctionne

## 🛠️ DÉVELOPPEMENT ORGANISÉ

### Lors de l'ajout de nouveaux fichiers :
1. **TOUJOURS** déterminer la catégorie (core/utils/tests/docs)
2. **JAMAIS** placer à la racine sauf cas exceptionnel
3. **SYSTÉMATIQUEMENT** mettre à jour les imports si nécessaire
4. **OBLIGATOIREMENT** tester que le système fonctionne encore

### Règles de Nommage
```python
# ✅ CORRECT - Fichier dans le bon dossier
scripts/core/new_analyzer.py
scripts/utils/new_validator.py
scripts/tests/test_new_feature.py
docs/NEW_GUIDE.md

# ❌ INCORRECT - Fichier à la racine
new_analyzer.py
new_validator.py
test_new_feature.py
NEW_GUIDE.md
```

## 🔧 MAINTENANCE DE L'ORGANISATION

### Actions Autorisées
- ✅ **Modifier** le contenu des fichiers existants
- ✅ **Ajouter** de nouveaux fichiers dans les bons dossiers
- ✅ **Renommer** des fichiers en gardant la même structure
- ✅ **Supprimer** des fichiers obsolètes

### Actions INTERDITES
- ❌ **Déplacer** des fichiers vers la racine
- ❌ **Créer** de nouveaux dossiers à la racine
- ❌ **Mélanger** les catégories (core/utils/tests)
- ❌ **Dupliquer** des fichiers entre dossiers

## 🎯 OBJECTIF FINAL

**Maintenir un système qui :**
- 🎯 **Reste parfaitement organisé** avec une structure claire
- 🛡️ **Facilite la maintenance** avec une séparation logique
- 🔍 **Permet de trouver rapidement** n'importe quel fichier
- 📊 **Respecte les bonnes pratiques** de développement logiciel

### 🚀 Interface Unifiée Préservée

**Point d'entrée unique** : `python arc_enhanced_prompt.py`

**Structure logique** :
- `scripts/core/` → Fonctionnalités principales
- `scripts/utils/` → Outils d'aide
- `scripts/tests/` → Tests et validation
- `docs/` → Documentation

---

**RAPPEL CRITIQUE** : Une structure désorganisée rend le système impossible à maintenir. L'organisation n'est pas optionnelle, elle est **VITALE** pour la productivité et la maintenabilité du projet.