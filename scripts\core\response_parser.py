"""
Analyseur de réponse IA pour le système d'analyse automatisée ARC AGI.

Ce module contient la classe ResponseParser qui extrait et structure
les informations des réponses générées par l'IA, incluant les transformations,
patterns, règles et grilles de solution proposées.
"""

import re
import json
import numpy as np
from typing import Optional, List, Dict, Any, Tuple
import logging

from .data_models import ParsedResponse
from .constants import RegexPatterns, SystemLimits, ColorCodes


class ResponseParsingError(Exception):
    """Exception levée lors d'erreurs de parsing de réponse."""
    pass


class ResponseParser:
    """
    Analyseur de réponse IA qui extrait les informations structurées
    des réponses textuelles générées par l'IA.
    """
    
    def __init__(self):
        """Initialise l'analyseur avec les patterns de parsing."""
        self.logger = logging.getLogger(__name__)
        
        # Patterns regex pour l'extraction
        self.grid_patterns = [
            # Format standard avec crochets
            r'\[\[.*?\]\]',
            # Format avec parenthèses
            r'\(\(.*?\)\)',
            # Format numérique simple
            r'(\d+\s*,?\s*)*\d+',
            # Format avec séparateurs
            r'(\d+[\s,|;]*)+',
        ]
        
        # Patterns pour les sections
        self.section_patterns = {
            'transformations': [
                r'(?i)transformations?\s*:?\s*(.*?)(?=\n\n|\n[A-Z]|$)',
                r'(?i)(?:detected|found)\s+transformations?\s*:?\s*(.*?)(?=\n\n|\n[A-Z]|$)',
                r'(?i)types?\s+de\s+transformations?\s*:?\s*(.*?)(?=\n\n|\n[A-Z]|$)'
            ],
            'patterns': [
                r'(?i)patterns?\s*:?\s*(.*?)(?=\n\n|\n[A-Z]|$)',
                r'(?i)motifs?\s*:?\s*(.*?)(?=\n\n|\n[A-Z]|$)',
                r'(?i)(?:detected|found)\s+patterns?\s*:?\s*(.*?)(?=\n\n|\n[A-Z]|$)'
            ],
            'rules': [
                r'(?i)r[uè]gles?\s*:?\s*(.*?)(?=\n\n|\n[A-Z]|$)',
                r'(?i)logic(?:al)?\s+rules?\s*:?\s*(.*?)(?=\n\n|\n[A-Z]|$)',
                r'(?i)conditions?\s*:?\s*(.*?)(?=\n\n|\n[A-Z]|$)'
            ],
            'reasoning': [
                r'(?i)raisonnement\s*:?\s*(.*?)(?=\n\n|\n[A-Z]|$)',
                r'(?i)reasoning\s*:?\s*(.*?)(?=\n\n|\n[A-Z]|$)',
                r'(?i)étapes?\s*:?\s*(.*?)(?=\n\n|\n[A-Z]|$)',
                r'(?i)steps?\s*:?\s*(.*?)(?=\n\n|\n[A-Z]|$)'
            ],
            'interpretation': [
                r'(?i)interprétation\s*:?\s*(.*?)(?=\n\n|\n[A-Z]|$)',
                r'(?i)interpretation\s*:?\s*(.*?)(?=\n\n|\n[A-Z]|$)',
                r'(?i)analyse\s*:?\s*(.*?)(?=\n\n|\n[A-Z]|$)',
                r'(?i)analysis\s*:?\s*(.*?)(?=\n\n|\n[A-Z]|$)'
            ]
        }
    
    def parse_ai_response(self, response: str) -> ParsedResponse:
        """
        Parse une réponse IA complète et extrait toutes les informations structurées.
        
        Args:
            response: Réponse textuelle de l'IA
            
        Returns:
            ParsedResponse: Objet contenant toutes les informations extraites
            
        Raises:
            ResponseParsingError: Si le parsing échoue
        """
        if not response or not response.strip():
            raise ResponseParsingError("Réponse vide ou invalide")
        
        if len(response) > SystemLimits.MAX_RESPONSE_LENGTH:
            self.logger.warning(f"Réponse tronquée à {SystemLimits.MAX_RESPONSE_LENGTH} caractères")
            response = response[:SystemLimits.MAX_RESPONSE_LENGTH]
        
        try:
            # Extraction des différentes sections
            transformations = self._extract_transformations(response)
            patterns = self._extract_patterns(response)
            rules = self._extract_rules(response)
            reasoning_steps = self._extract_reasoning_steps(response)
            interpretation = self._extract_interpretation(response)
            proposed_grid = self.extract_solution_grid(response)
            
            return ParsedResponse(
                transformations=transformations,
                patterns=patterns,
                rules=rules,
                proposed_grid=proposed_grid,
                reasoning_steps=reasoning_steps,
                interpretation=interpretation,
                raw_response=response
            )
            
        except Exception as e:
            self.logger.error(f"Erreur lors du parsing de la réponse: {e}")
            raise ResponseParsingError(f"Échec du parsing: {e}")
    
    def extract_solution_grid(self, response: str) -> Optional[np.ndarray]:
        """
        Extrait la grille de solution proposée de la réponse IA.
        
        Args:
            response: Réponse textuelle de l'IA
            
        Returns:
            np.ndarray ou None: Grille extraite ou None si non trouvée
        """
        if not response:
            return None
        
        # Essayer différents formats de grille - priorité aux formats structurés
        structured_patterns = [r'\[\[.*?\]\]', r'\(\(.*?\)\)']
        for pattern in structured_patterns:
            grid = self._try_extract_grid_with_pattern(response, pattern)
            if grid is not None:
                return grid
        
        # Puis essayer les formats numériques seulement si pas de format structuré
        # et seulement si le texte ne contient pas de crochets ou parenthèses
        if not re.search(r'[\[\]()]', response):
            numeric_patterns = [r'(\d+\s*,?\s*)*\d+', r'(\d+[\s,|;]*)+']
            for pattern in numeric_patterns:
                grid = self._try_extract_grid_with_pattern(response, pattern)
                if grid is not None:
                    return grid
        
        # Essayer d'extraire depuis des sections spécifiques
        grid_sections = [
            r'(?i)(?:output|solution|grille)\s*:?\s*(.*?)(?=\n\n|\n[A-Z]|$)',
            r'(?i)(?:proposed|final)\s+(?:grid|grille)\s*:?\s*(.*?)(?=\n\n|\n[A-Z]|$)',
            r'(?i)résultat\s*:?\s*(.*?)(?=\n\n|\n[A-Z]|$)'
        ]
        
        for section_pattern in grid_sections:
            match = re.search(section_pattern, response, re.DOTALL)
            if match:
                section_text = match.group(1)
                # Priorité aux formats structurés (crochets/parenthèses)
                structured_patterns = [r'\[\[.*?\]\]', r'\(\(.*?\)\)']
                for grid_pattern in structured_patterns:
                    grid = self._try_extract_grid_with_pattern(section_text, grid_pattern)
                    if grid is not None:
                        return grid
                # Puis essayer les formats numériques seulement si pas de crochets/parenthèses
                if not re.search(r'[\[\]()]', section_text):
                    numeric_patterns = [r'(\d+\s*,?\s*)*\d+', r'(\d+[\s,|;]*)+']
                    for grid_pattern in numeric_patterns:
                        grid = self._try_extract_grid_with_pattern(section_text, grid_pattern)
                        if grid is not None:
                            return grid
        
        self.logger.warning("Aucune grille de solution trouvée dans la réponse")
        return None
    
    def _try_extract_grid_with_pattern(self, text: str, pattern: str) -> Optional[np.ndarray]:
        """
        Essaie d'extraire une grille avec un pattern spécifique.
        
        Args:
            text: Texte à analyser
            pattern: Pattern regex à utiliser
            
        Returns:
            np.ndarray ou None: Grille extraite ou None si échec
        """
        try:
            matches = re.findall(pattern, text, re.DOTALL)
            if not matches:
                return None
            
            # Prendre la dernière grille trouvée (souvent la solution finale)
            grid_text = matches[-1]
            
            # Essayer différentes méthodes de parsing selon le pattern
            if pattern == r'\[\[.*?\]\]':
                # Format crochets - utiliser seulement bracket et json parsers
                parsers = [self._parse_bracket_format, self._parse_json_format]
            elif pattern == r'\(\(.*?\)\)':
                # Format parenthèses - adapter pour les crochets
                grid_text = grid_text.replace('(', '[').replace(')', ']')
                parsers = [self._parse_bracket_format, self._parse_json_format]
            else:
                # Formats numériques
                parsers = [self._parse_numeric_format, self._parse_separated_format]
            
            for parser in parsers:
                try:
                    grid = parser(grid_text)
                    if self._validate_grid(grid):
                        return grid
                except Exception as parse_error:
                    self.logger.debug(f"Parser {parser.__name__} failed: {parse_error}")
                    continue
            
            return None
            
        except Exception as e:
            self.logger.debug(f"Erreur lors de l'extraction avec pattern {pattern}: {e}")
            return None
    
    def _parse_bracket_format(self, grid_text: str) -> np.ndarray:
        """Parse une grille au format [[1,2,3],[4,5,6]]."""
        # Nettoyer le texte
        cleaned = re.sub(r'[^\d,\[\]]', '', grid_text)
        
        # Évaluer comme Python (sécurisé car on a nettoyé)
        try:
            grid_list = eval(cleaned)
            grid_array = np.array(grid_list, dtype=int)
            
            # Vérifier que toutes les valeurs sont des couleurs valides
            if not np.all(np.isin(grid_array, ColorCodes.ALL_COLORS)):
                raise ValueError("Couleurs invalides détectées")
                
            return grid_array
        except:
            # Essayer avec json
            try:
                grid_list = json.loads(cleaned)
                grid_array = np.array(grid_list, dtype=int)
                
                # Vérifier que toutes les valeurs sont des couleurs valides
                if not np.all(np.isin(grid_array, ColorCodes.ALL_COLORS)):
                    raise ValueError("Couleurs invalides détectées")
                    
                return grid_array
            except:
                raise ValueError("Format de grille invalide")
    
    def _parse_json_format(self, grid_text: str) -> np.ndarray:
        """Parse une grille au format JSON."""
        try:
            grid_list = json.loads(grid_text)
            grid_array = np.array(grid_list, dtype=int)
            
            # Vérifier que toutes les valeurs sont des couleurs valides
            if not np.all(np.isin(grid_array, ColorCodes.ALL_COLORS)):
                raise ValueError("Couleurs invalides détectées")
                
            return grid_array
        except:
            # Essayer de réparer le JSON
            try:
                repaired = re.sub(r'([^\[\],\d])', '', grid_text)
                grid_list = json.loads(repaired)
                grid_array = np.array(grid_list, dtype=int)
                
                # Vérifier que toutes les valeurs sont des couleurs valides
                if not np.all(np.isin(grid_array, ColorCodes.ALL_COLORS)):
                    raise ValueError("Couleurs invalides détectées")
                    
                return grid_array
            except:
                raise ValueError("Format JSON invalide")
    
    def _parse_numeric_format(self, grid_text: str) -> np.ndarray:
        """Parse une grille au format numérique simple."""
        # Extraire tous les nombres (seulement les chiffres simples 0-9)
        numbers = re.findall(r'\b([0-9])\b', grid_text)
        if not numbers:
            # Essayer avec des nombres plus longs mais vérifier qu'ils sont valides
            all_numbers = re.findall(r'\d+', grid_text)
            numbers = [n for n in all_numbers if len(n) == 1 and 0 <= int(n) <= 9]
            if not numbers:
                raise ValueError("Aucun nombre valide trouvé")
        
        numbers = [int(n) for n in numbers]
        
        # Vérifier que toutes les valeurs sont des couleurs valides
        if not all(0 <= n <= 9 for n in numbers):
            raise ValueError("Couleurs invalides détectées")
        
        # Essayer de deviner les dimensions
        possible_sizes = []
        for size in range(1, int(len(numbers)**0.5) + 1):
            if len(numbers) % size == 0:
                possible_sizes.append((size, len(numbers) // size))
        
        # Prendre la taille la plus carrée
        if possible_sizes:
            rows, cols = min(possible_sizes, key=lambda x: abs(x[0] - x[1]))
            return np.array(numbers[:rows*cols]).reshape(rows, cols)
        
        raise ValueError("Impossible de déterminer les dimensions")
    
    def _parse_separated_format(self, grid_text: str) -> np.ndarray:
        """Parse une grille avec séparateurs variés."""
        # Diviser en lignes
        lines = [line.strip() for line in grid_text.split('\n') if line.strip()]
        
        grid_rows = []
        for line in lines:
            # Extraire les nombres de chaque ligne
            numbers = re.findall(r'\d+', line)
            if numbers:
                row = [int(n) for n in numbers]
                # Vérifier que toutes les valeurs sont des couleurs valides
                if all(0 <= n <= 9 for n in row):
                    grid_rows.append(row)
        
        if grid_rows:
            grid_array = np.array(grid_rows, dtype=int)
            
            # Vérifier que toutes les valeurs sont des couleurs valides
            if not np.all(np.isin(grid_array, ColorCodes.ALL_COLORS)):
                raise ValueError("Couleurs invalides détectées")
                
            return grid_array
        
        raise ValueError("Aucune ligne valide trouvée")
    
    def _validate_grid(self, grid: np.ndarray) -> bool:
        """
        Valide qu'une grille respecte les contraintes ARC.
        
        Args:
            grid: Grille à valider
            
        Returns:
            bool: True si valide, False sinon
        """
        if grid is None or grid.size == 0:
            return False
        
        # Vérifier les dimensions
        if len(grid.shape) != 2:
            return False
        
        rows, cols = grid.shape
        if not (SystemLimits.MIN_GRID_SIZE <= rows <= SystemLimits.MAX_GRID_SIZE):
            return False
        if not (SystemLimits.MIN_GRID_SIZE <= cols <= SystemLimits.MAX_GRID_SIZE):
            return False
        
        # Vérifier que toutes les valeurs sont des couleurs valides
        if not np.all(np.isin(grid, ColorCodes.ALL_COLORS)):
            return False
        
        return True
    
    def _extract_transformations(self, response: str) -> List[str]:
        """Extrait les transformations détectées."""
        return self._extract_list_items(response, self.section_patterns['transformations'])
    
    def _extract_patterns(self, response: str) -> List[str]:
        """Extrait les patterns identifiés."""
        return self._extract_list_items(response, self.section_patterns['patterns'])
    
    def _extract_rules(self, response: str) -> List[str]:
        """Extrait les règles déduites."""
        return self._extract_list_items(response, self.section_patterns['rules'])
    
    def _extract_reasoning_steps(self, response: str) -> List[str]:
        """Extrait les étapes de raisonnement."""
        steps = []
        
        # Essayer d'extraire depuis les sections de raisonnement
        for pattern in self.section_patterns['reasoning']:
            match = re.search(pattern, response, re.DOTALL)
            if match:
                section_text = match.group(1)
                # Extraire les étapes numérotées
                step_matches = re.findall(r'^\s*(\d+\.?\s*.*?)(?=\n\d+\.|\n\n|$)', 
                                        section_text, re.MULTILINE)
                if step_matches:
                    steps.extend([step.strip() for step in step_matches])
        
        # Si pas d'étapes trouvées, chercher dans tout le texte
        if not steps:
            step_matches = re.findall(r'^\s*(\d+\.?\s*.*?)(?=\n\d+\.|\n\n|$)', 
                                    response, re.MULTILINE)
            steps = [step.strip() for step in step_matches[:SystemLimits.MAX_REASONING_STEPS]]
        
        return steps
    
    def _extract_interpretation(self, response: str) -> str:
        """Extrait l'interprétation générale."""
        for pattern in self.section_patterns['interpretation']:
            match = re.search(pattern, response, re.DOTALL)
            if match:
                return match.group(1).strip()
        
        # Si pas d'interprétation spécifique, prendre le début de la réponse
        lines = response.split('\n')
        interpretation_lines = []
        for line in lines[:10]:  # Prendre les 10 premières lignes max
            if line.strip() and not re.match(r'^\d+\.', line.strip()):
                interpretation_lines.append(line.strip())
            if len(interpretation_lines) >= 3:
                break
        
        return ' '.join(interpretation_lines) if interpretation_lines else ""
    
    def _extract_list_items(self, response: str, patterns: List[str]) -> List[str]:
        """
        Extrait une liste d'éléments depuis une section de la réponse.
        
        Args:
            response: Réponse complète
            patterns: Patterns regex pour trouver la section
            
        Returns:
            List[str]: Liste des éléments extraits
        """
        items = []
        
        for pattern in patterns:
            match = re.search(pattern, response, re.DOTALL)
            if match:
                section_text = match.group(1)
                
                # Extraire les éléments de liste (-, *, •, numérotés)
                list_patterns = [
                    r'^\s*[-*•]\s*(.+?)(?=\n\s*[-*•]|\n\n|$)',
                    r'^\s*\d+\.?\s*(.+?)(?=\n\s*\d+\.|\n\n|$)',
                    r'^\s*[a-zA-Z]\)\s*(.+?)(?=\n\s*[a-zA-Z]\)|\n\n|$)'
                ]
                
                for list_pattern in list_patterns:
                    list_matches = re.findall(list_pattern, section_text, re.MULTILINE)
                    if list_matches:
                        items.extend([item.strip() for item in list_matches if item.strip()])
                        break
                
                # Si pas de liste structurée, diviser par lignes
                if not items:
                    lines = [line.strip() for line in section_text.split('\n') 
                           if line.strip() and len(line.strip()) > 3]
                    items.extend(lines[:5])  # Max 5 éléments
                
                break
        
        return items[:10]  # Limiter à 10 éléments maximum