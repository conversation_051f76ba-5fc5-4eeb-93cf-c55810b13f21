#!/usr/bin/env python3
"""
Tests de conformité pour l'organisation des fichiers ARC AGI.

Ce module teste que la structure des fichiers respecte strictement les règles
définies dans file_organization_rules.md.
"""

import os
import unittest
from pathlib import Path
from typing import List, Set


class TestFileOrganizationCompliance(unittest.TestCase):
    """Tests de conformité pour l'organisation des fichiers."""
    
    def setUp(self):
        """Configuration des tests."""
        self.root_dir = Path(__file__).parent.parent.parent
        self.scripts_dir = self.root_dir / "scripts"
        self.core_dir = self.scripts_dir / "core"
        self.utils_dir = self.scripts_dir / "utils"
        self.tests_dir = self.scripts_dir / "tests"
        self.docs_dir = self.root_dir / "docs"
    
    def test_single_entry_point_at_root(self):
        """Test que seul arc_enhanced_prompt.py est autorisé comme script Python à la racine."""
        python_files_at_root = []
        
        for item in self.root_dir.iterdir():
            if item.is_file() and item.suffix == '.py':
                python_files_at_root.append(item.name)
        
        # Seuls les fichiers autorisés à la racine
        allowed_python_files = {'arc_enhanced_prompt.py', 'cleanup_old_files.py'}
        
        for py_file in python_files_at_root:
            self.assertIn(
                py_file, 
                allowed_python_files,
                f"Fichier Python non autorisé à la racine: {py_file}. "
                f"Seuls {allowed_python_files} sont autorisés."
            )
    
    def test_no_forbidden_files_at_root(self):
        """Test qu'aucun fichier interdit n'est présent à la racine."""
        forbidden_patterns = [
            'generate_*.py',
            '*_analysis.py', 
            'arc_analyzer.py',
            'test_*.py',
            'extract_*.py',
            'validate_*.py',
            'README_*.md',
            'SUMMARY_*.md',
            'GUIDE_*.md',
            'MIGRATION_*.md',
            'TUTORIAL_*.md',
            'ORGANIZATION_*.md',
            'STRUCTURE_*.md',
            'FINAL_*.md',
            'COMPLETE_*.md'
        ]
        
        root_files = [f.name for f in self.root_dir.iterdir() if f.is_file()]
        
        for pattern in forbidden_patterns:
            import fnmatch
            matching_files = fnmatch.filter(root_files, pattern)
            self.assertEqual(
                len(matching_files), 0,
                f"Fichiers interdits trouvés à la racine avec le pattern '{pattern}': {matching_files}"
            )
    
    def test_core_directory_structure(self):
        """Test que le répertoire scripts/core/ contient les bons fichiers."""
        self.assertTrue(
            self.core_dir.exists(),
            "Le répertoire scripts/core/ doit exister"
        )
        
        # Fichiers obligatoires dans core/
        required_core_files = {
            '__init__.py',
            'arc_analyzer.py',
            'generate_enhanced_prompt.py',
            'generate_analysis.py',
            'arc_prompt_generator4.py',
            # Nouveaux fichiers du système automatisé
            'automated_analyzer.py',
            'secure_puzzle_loader.py',
            'chat_session.py',
            'response_parser.py',
            'solution_validator.py',
            'learning_system.py',
            'data_models.py',
            'exceptions.py',
            'constants.py'
        }
        
        core_files = {f.name for f in self.core_dir.iterdir() if f.is_file() and f.suffix == '.py'}
        
        for required_file in required_core_files:
            self.assertIn(
                required_file,
                core_files,
                f"Fichier obligatoire manquant dans scripts/core/: {required_file}"
            )
    
    def test_utils_directory_structure(self):
        """Test que le répertoire scripts/utils/ contient les bons fichiers."""
        self.assertTrue(
            self.utils_dir.exists(),
            "Le répertoire scripts/utils/ doit exister"
        )
        
        # Fichiers obligatoires dans utils/
        required_utils_files = {
            '__init__.py',
            'extract_key_insights.py',
            'validate_generic_insights.py'
        }
        
        utils_files = {f.name for f in self.utils_dir.iterdir() if f.is_file() and f.suffix == '.py'}
        
        for required_file in required_utils_files:
            self.assertIn(
                required_file,
                utils_files,
                f"Fichier obligatoire manquant dans scripts/utils/: {required_file}"
            )
    
    def test_tests_directory_structure(self):
        """Test que le répertoire scripts/tests/ contient les bons fichiers."""
        self.assertTrue(
            self.tests_dir.exists(),
            "Le répertoire scripts/tests/ doit exister"
        )
        
        # Vérifier que tous les fichiers de test sont bien dans tests/
        test_files = {f.name for f in self.tests_dir.iterdir() if f.is_file() and f.suffix == '.py'}
        
        # Tous les fichiers doivent commencer par 'test_' ou être '__init__.py'
        for test_file in test_files:
            self.assertTrue(
                test_file.startswith('test_') or test_file == '__init__.py',
                f"Fichier non conforme dans scripts/tests/: {test_file}. "
                f"Les fichiers de test doivent commencer par 'test_'"
            )
    
    def test_no_misplaced_files(self):
        """Test qu'aucun fichier n'est mal placé entre les répertoires."""
        # Vérifier qu'aucun script core n'est dans utils/ ou tests/
        core_patterns = ['arc_analyzer.py', 'generate_*.py', 'automated_analyzer.py']
        
        if self.utils_dir.exists():
            utils_files = [f.name for f in self.utils_dir.iterdir() if f.is_file()]
            for pattern in core_patterns:
                import fnmatch
                matching_files = fnmatch.filter(utils_files, pattern)
                self.assertEqual(
                    len(matching_files), 0,
                    f"Scripts core mal placés dans utils/: {matching_files}"
                )
        
        if self.tests_dir.exists():
            test_files = [f.name for f in self.tests_dir.iterdir() if f.is_file()]
            for pattern in core_patterns:
                import fnmatch
                matching_files = fnmatch.filter(test_files, pattern)
                # Exclure les vrais fichiers de test
                non_test_matches = [f for f in matching_files if not f.startswith('test_')]
                self.assertEqual(
                    len(non_test_matches), 0,
                    f"Scripts core mal placés dans tests/: {non_test_matches}"
                )
        
        # Vérifier qu'aucun utilitaire n'est dans core/ ou tests/
        utils_patterns = ['extract_*.py', 'validate_*.py']
        
        if self.core_dir.exists():
            core_files = [f.name for f in self.core_dir.iterdir() if f.is_file()]
            for pattern in utils_patterns:
                import fnmatch
                matching_files = fnmatch.filter(core_files, pattern)
                self.assertEqual(
                    len(matching_files), 0,
                    f"Utilitaires mal placés dans core/: {matching_files}"
                )
    
    def test_docs_directory_structure(self):
        """Test que la documentation est bien organisée dans docs/."""
        if self.docs_dir.exists():
            # Vérifier qu'aucun fichier .md n'est à la racine (sauf README.md)
            root_md_files = [f.name for f in self.root_dir.iterdir() 
                           if f.is_file() and f.suffix == '.md' and f.name != 'README.md']
            
            self.assertEqual(
                len(root_md_files), 0,
                f"Fichiers .md non autorisés à la racine: {root_md_files}. "
                f"Seul README.md est autorisé à la racine."
            )
    
    def test_entry_point_functionality(self):
        """Test que le point d'entrée unique fonctionne correctement."""
        entry_point = self.root_dir / "arc_enhanced_prompt.py"
        
        self.assertTrue(
            entry_point.exists(),
            "Le point d'entrée arc_enhanced_prompt.py doit exister"
        )
        
        # Vérifier que le fichier est exécutable (contient if __name__ == "__main__")
        with open(entry_point, 'r', encoding='utf-8') as f:
            content = f.read()
            self.assertIn(
                'if __name__ == "__main__"',
                content,
                "Le point d'entrée doit contenir 'if __name__ == \"__main__\"'"
            )
    
    def test_directory_structure_integrity(self):
        """Test que la structure des répertoires est intègre."""
        required_directories = [
            self.scripts_dir,
            self.core_dir,
            self.utils_dir,
            self.tests_dir
        ]
        
        for directory in required_directories:
            self.assertTrue(
                directory.exists(),
                f"Répertoire obligatoire manquant: {directory}"
            )
            
            # Vérifier que chaque répertoire a un __init__.py
            init_file = directory / "__init__.py"
            self.assertTrue(
                init_file.exists(),
                f"Fichier __init__.py manquant dans: {directory}"
            )
    
    def test_no_duplicate_functionality(self):
        """Test qu'il n'y a pas de duplication de fonctionnalités entre répertoires."""
        # Vérifier qu'il n'y a pas de fichiers avec le même nom dans différents répertoires
        all_python_files = {}
        
        directories_to_check = [self.core_dir, self.utils_dir, self.tests_dir]
        
        for directory in directories_to_check:
            if directory.exists():
                for py_file in directory.glob("*.py"):
                    if py_file.name != "__init__.py":
                        if py_file.name in all_python_files:
                            self.fail(
                                f"Fichier dupliqué trouvé: {py_file.name} "
                                f"dans {directory} et {all_python_files[py_file.name]}"
                            )
                        all_python_files[py_file.name] = directory


def run_compliance_tests():
    """Fonction utilitaire pour exécuter les tests de conformité."""
    unittest.main(verbosity=2)


if __name__ == "__main__":
    run_compliance_tests()