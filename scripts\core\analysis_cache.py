#!/usr/bin/env python3
"""
Gestionnaire de cache pour les analyses ARC AGI.

Ce module gère le cache des analyses existantes pour optimiser les performances
et éviter de régénérer des analyses déjà disponibles.
"""

import os
import json
import hashlib
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging

from .data_models import PuzzleData
from .constants import SystemLimits


class AnalysisCache:
    """
    Gestionnaire de cache pour les analyses ARC AGI.
    
    Cette classe gère:
    - La détection des analyses existantes
    - La validation de la fraîcheur du cache
    - L'optimisation des temps de chargement
    - La détection des analyses obsolètes
    """
    
    def __init__(self, cache_dir: Path, max_age_days: int = 30):
        """
        Initialise le gestionnaire de cache.
        
        Args:
            cache_dir: Répertoire de cache des analyses
            max_age_days: Âge maximum en jours avant qu'une analyse soit considérée obsolète
        """
        self.cache_dir = Path(cache_dir)
        self.max_age_days = max_age_days
        self.logger = logging.getLogger(__name__)
        
        # Créer le répertoire de cache si nécessaire
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Statistiques de cache
        self.stats = {
            'hits': 0,
            'misses': 0,
            'obsolete_removed': 0,
            'total_size_mb': 0
        }
    
    def get_analysis_path(self, puzzle_id: str, subset: str) -> Path:
        """
        Retourne le chemin du fichier d'analyse pour un puzzle.
        
        Args:
            puzzle_id: ID du puzzle
            subset: Subset (training/evaluation)
            
        Returns:
            Path: Chemin du fichier d'analyse
        """
        return self.cache_dir / subset / f"{puzzle_id}_analysis.json"
    
    def is_analysis_cached(self, puzzle_id: str, subset: str) -> bool:
        """
        Vérifie si une analyse est en cache et valide.
        
        Cette méthode vérifie:
        1. L'existence du fichier d'analyse
        2. L'âge du fichier (non obsolète)
        3. L'intégrité du contenu JSON
        4. La présence des champs requis
        
        Args:
            puzzle_id: ID du puzzle (ex: "007bbfb7")
            subset: Subset du puzzle ("training" ou "evaluation")
            
        Returns:
            bool: True si l'analyse est en cache et valide, False sinon
            
        Note:
            Les analyses obsolètes sont automatiquement supprimées
            lors de cette vérification.
        """
        analysis_path = self.get_analysis_path(puzzle_id, subset)
        
        if not analysis_path.exists():
            self.logger.debug(f"Analyse non trouvée en cache: {puzzle_id}")
            return False
        
        # Vérifier l'âge du fichier
        if self._is_analysis_obsolete(analysis_path):
            self.logger.info(f"Analyse obsolète détectée: {puzzle_id}")
            self._remove_obsolete_analysis(analysis_path)
            return False
        
        # Vérifier l'intégrité du fichier
        if not self._is_analysis_valid(analysis_path):
            self.logger.warning(f"Analyse corrompue détectée: {puzzle_id}")
            self._remove_obsolete_analysis(analysis_path)
            return False
        
        self.logger.debug(f"Analyse trouvée en cache: {puzzle_id}")
        return True
    
    def load_cached_analysis(self, puzzle_id: str, subset: str) -> Optional[Dict[str, Any]]:
        """
        Charge une analyse depuis le cache.
        
        Cette méthode:
        1. Vérifie d'abord si l'analyse est en cache et valide
        2. Charge le fichier JSON depuis le disque
        3. Met à jour les statistiques de cache (hits/misses)
        4. Gère les erreurs de lecture et supprime les fichiers corrompus
        
        Args:
            puzzle_id: ID du puzzle (ex: "007bbfb7")
            subset: Subset du puzzle ("training" ou "evaluation")
            
        Returns:
            Dict contenant l'analyse avec les clés:
            - puzzle_id: ID du puzzle
            - subset: Subset du puzzle
            - raw_analysis: Données d'analyse brutes
            - cache_metadata: Métadonnées de cache
            
            None si l'analyse n'est pas disponible ou corrompue
            
        Raises:
            Aucune exception - les erreurs sont gérées en interne
            et loggées appropriément.
        """
        if not self.is_analysis_cached(puzzle_id, subset):
            self.stats['misses'] += 1
            return None
        
        analysis_path = self.get_analysis_path(puzzle_id, subset)
        
        try:
            with open(analysis_path, 'r', encoding='utf-8') as f:
                analysis_data = json.load(f)
            
            self.stats['hits'] += 1
            self.logger.debug(f"Analyse chargée depuis le cache: {puzzle_id}")
            return analysis_data
            
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement de l'analyse {puzzle_id}: {e}")
            self._remove_obsolete_analysis(analysis_path)
            self.stats['misses'] += 1
            return None
    
    def save_analysis_to_cache(self, puzzle_id: str, subset: str, analysis_data: Dict[str, Any]) -> bool:
        """
        Sauvegarde une analyse dans le cache.
        
        Cette méthode:
        1. Crée le répertoire de cache si nécessaire
        2. Ajoute des métadonnées de cache (timestamp, version)
        3. Sauvegarde le fichier JSON avec indentation
        4. Gère les erreurs d'écriture
        
        Args:
            puzzle_id: ID du puzzle (ex: "007bbfb7")
            subset: Subset du puzzle ("training" ou "evaluation")
            analysis_data: Données d'analyse à sauvegarder, doit contenir:
                - puzzle_id: ID du puzzle
                - subset: Subset du puzzle
                - raw_analysis: Données d'analyse brutes
                
        Returns:
            bool: True si la sauvegarde a réussi, False en cas d'erreur
            
        Note:
            Les métadonnées de cache sont automatiquement ajoutées:
            - cached_at: Timestamp ISO de la sauvegarde
            - cache_version: Version du système de cache
            - puzzle_id: ID du puzzle (redondant pour validation)
            - subset: Subset du puzzle (redondant pour validation)
        """
        analysis_path = self.get_analysis_path(puzzle_id, subset)
        
        try:
            # Créer le répertoire si nécessaire
            analysis_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Ajouter des métadonnées de cache
            cache_metadata = {
                'cached_at': datetime.now().isoformat(),
                'cache_version': '1.0',
                'puzzle_id': puzzle_id,
                'subset': subset
            }
            
            analysis_data['cache_metadata'] = cache_metadata
            
            # Nettoyer les données pour la sérialisation JSON
            clean_data = self._clean_for_json(analysis_data)
            
            # Sauvegarder avec indentation pour la lisibilité
            with open(analysis_path, 'w', encoding='utf-8') as f:
                json.dump(clean_data, f, indent=2, ensure_ascii=False)
            
            self.logger.debug(f"Analyse sauvegardée en cache: {puzzle_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la sauvegarde de l'analyse {puzzle_id}: {e}")
            return False
    
    def _clean_for_json(self, data):
        """
        Nettoie les données pour la sérialisation JSON en convertissant les types non-sérialisables.
        
        Args:
            data: Données à nettoyer
            
        Returns:
            Données nettoyées et sérialisables en JSON
        """
        import numpy as np
        
        if isinstance(data, dict):
            # Nettoyer les clés et valeurs du dictionnaire
            clean_dict = {}
            for key, value in data.items():
                # Convertir les clés en string si nécessaire
                if isinstance(key, (np.integer, np.floating)):
                    clean_key = str(key)
                elif isinstance(key, np.bool_):
                    clean_key = str(bool(key))
                else:
                    clean_key = str(key) if not isinstance(key, (str, int, float, bool)) else key
                
                clean_dict[clean_key] = self._clean_for_json(value)
            return clean_dict
        elif isinstance(data, list):
            return [self._clean_for_json(item) for item in data]
        elif isinstance(data, np.ndarray):
            return data.tolist()  # Convertir numpy array en liste Python
        elif isinstance(data, np.integer):
            return int(data)  # Convertir numpy int en int Python
        elif isinstance(data, np.floating):
            return float(data)  # Convertir numpy float en float Python
        elif isinstance(data, np.bool_):
            return bool(data)  # Convertir numpy bool en bool Python
        elif isinstance(data, set):
            return list(data)  # Convertir set en liste Python
        elif isinstance(data, tuple):
            return list(data)  # Convertir tuple en liste Python
        else:
            return data  # Retourner tel quel pour les types déjà sérialisables
    
    def _is_analysis_obsolete(self, analysis_path: Path) -> bool:
        """
        Vérifie si une analyse est obsolète basée sur son âge.
        
        Args:
            analysis_path: Chemin du fichier d'analyse
            
        Returns:
            bool: True si l'analyse est obsolète
        """
        try:
            # Vérifier l'âge du fichier
            file_time = datetime.fromtimestamp(analysis_path.stat().st_mtime)
            max_age = datetime.now() - timedelta(days=self.max_age_days)
            
            if file_time < max_age:
                return True
            
            # Vérifier les métadonnées de cache si disponibles
            with open(analysis_path, 'r', encoding='utf-8') as f:
                try:
                    data = json.load(f)
                except json.JSONDecodeError as e:
                    self.logger.warning(f"Fichier JSON corrompu {analysis_path}: {e}")
                    # Essayer de supprimer le fichier corrompu
                    try:
                        analysis_path.unlink(missing_ok=True)
                    except (PermissionError, OSError) as delete_error:
                        self.logger.warning(f"Impossible de supprimer le fichier corrompu: {delete_error}")
                    return True  # Considérer comme obsolète
            
            cache_metadata = data.get('cache_metadata', {})
            if 'cached_at' in cache_metadata:
                cached_time = datetime.fromisoformat(cache_metadata['cached_at'])
                if cached_time < max_age:
                    return True
            
            return False
            
        except Exception as e:
            self.logger.warning(f"Erreur lors de la vérification d'obsolescence: {e}")
            return True  # Considérer comme obsolète en cas d'erreur
    
    def _is_analysis_valid(self, analysis_path: Path) -> bool:
        """
        Vérifie l'intégrité d'un fichier d'analyse.
        
        Args:
            analysis_path: Chemin du fichier d'analyse
            
        Returns:
            bool: True si l'analyse est valide
        """
        try:
            with open(analysis_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Vérifier les champs requis
            required_fields = ['puzzle_id', 'subset', 'raw_analysis']
            for field in required_fields:
                if field not in data:
                    self.logger.warning(f"Champ manquant dans l'analyse: {field}")
                    return False
            
            # Vérifier que raw_analysis n'est pas vide
            if not data['raw_analysis']:
                self.logger.warning("raw_analysis est vide")
                return False
            
            return True
            
        except json.JSONDecodeError as e:
            self.logger.warning(f"JSON invalide dans l'analyse: {e}")
            return False
        except Exception as e:
            self.logger.warning(f"Erreur lors de la validation de l'analyse: {e}")
            return False
    
    def _remove_obsolete_analysis(self, analysis_path: Path) -> None:
        """
        Supprime une analyse obsolète ou corrompue.
        
        Args:
            analysis_path: Chemin du fichier à supprimer
        """
        try:
            analysis_path.unlink()
            self.stats['obsolete_removed'] += 1
            self.logger.info(f"Analyse obsolète supprimée: {analysis_path}")
        except Exception as e:
            self.logger.error(f"Erreur lors de la suppression de l'analyse obsolète: {e}")
    
    def cleanup_obsolete_analyses(self) -> int:
        """
        Nettoie toutes les analyses obsolètes du cache.
        
        Returns:
            int: Nombre d'analyses supprimées
        """
        removed_count = 0
        
        for subset_dir in self.cache_dir.iterdir():
            if not subset_dir.is_dir():
                continue
            
            for analysis_file in subset_dir.glob("*_analysis.json"):
                if self._is_analysis_obsolete(analysis_file):
                    self._remove_obsolete_analysis(analysis_file)
                    removed_count += 1
        
        self.logger.info(f"Nettoyage terminé: {removed_count} analyses obsolètes supprimées")
        return removed_count
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Retourne les statistiques du cache.
        
        Returns:
            Dict contenant les statistiques
        """
        # Calculer la taille totale du cache
        total_size = 0
        file_count = 0
        
        for subset_dir in self.cache_dir.iterdir():
            if not subset_dir.is_dir():
                continue
            
            for analysis_file in subset_dir.glob("*_analysis.json"):
                try:
                    total_size += analysis_file.stat().st_size
                    file_count += 1
                except Exception:
                    pass
        
        self.stats['total_size_mb'] = round(total_size / (1024 * 1024), 2)
        self.stats['file_count'] = file_count
        
        # Calculer le taux de hit
        total_requests = self.stats['hits'] + self.stats['misses']
        hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        self.stats['hit_rate_percent'] = round(hit_rate, 1)
        
        return self.stats.copy()
    
    def preload_analyses(self, puzzle_ids: list, subset: str) -> Dict[str, Dict[str, Any]]:
        """
        Précharge plusieurs analyses en une seule opération.
        
        Args:
            puzzle_ids: Liste des IDs de puzzles à précharger
            subset: Subset (training/evaluation)
            
        Returns:
            Dict mapping puzzle_id -> analysis_data pour les analyses trouvées
        """
        preloaded = {}
        
        for puzzle_id in puzzle_ids:
            analysis_data = self.load_cached_analysis(puzzle_id, subset)
            if analysis_data:
                preloaded[puzzle_id] = analysis_data
        
        self.logger.info(f"Préchargement: {len(preloaded)}/{len(puzzle_ids)} analyses trouvées")
        return preloaded
    
    def get_cache_summary(self) -> str:
        """
        Retourne un résumé textuel du cache.
        
        Returns:
            str: Résumé du cache
        """
        stats = self.get_cache_stats()
        
        summary_lines = [
            "📊 RÉSUMÉ DU CACHE D'ANALYSES",
            f"Fichiers en cache: {stats['file_count']}",
            f"Taille totale: {stats['total_size_mb']} MB",
            f"Requêtes hits: {stats['hits']}",
            f"Requêtes misses: {stats['misses']}",
            f"Taux de hit: {stats['hit_rate_percent']}%",
            f"Analyses obsolètes supprimées: {stats['obsolete_removed']}"
        ]
        
        return "\n".join(summary_lines)