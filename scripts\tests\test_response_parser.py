"""
Tests pour l'analyseur de réponse IA (ResponseParser).

Ce module teste toutes les fonctionnalités du ResponseParser,
incluant l'extraction de grilles, transformations, patterns et règles
avec différents formats de réponse IA.
"""

import unittest
import numpy as np
from unittest.mock import patch, MagicMock
import sys
import os

# Ajouter le chemin des modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from scripts.core.response_parser import ResponseParser, ResponseParsingError
from scripts.core.data_models import ParsedResponse


class TestResponseParser(unittest.TestCase):
    """Tests pour la classe ResponseParser."""
    
    def setUp(self):
        """Configuration avant chaque test."""
        self.parser = ResponseParser()
    
    def test_init(self):
        """Test de l'initialisation du parser."""
        self.assertIsInstance(self.parser, ResponseParser)
        self.assertIsNotNone(self.parser.grid_patterns)
        self.assertIsNotNone(self.parser.section_patterns)
    
    def test_parse_ai_response_empty(self):
        """Test avec une réponse vide."""
        with self.assertRaises(ResponseParsingError):
            self.parser.parse_ai_response("")
        
        with self.assertRaises(ResponseParsingError):
            self.parser.parse_ai_response("   ")
    
    def test_parse_ai_response_complete(self):
        """Test avec une réponse IA complète et bien structurée."""
        response = """
        Transformations détectées:
        - Rotation de 90 degrés
        - Changement de couleur 1→2
        
        Patterns identifiés:
        - Symétrie horizontale
        - Répétition de motifs
        
        Règles déduites:
        - Si couleur bleue alors rotation
        - Maintenir la structure centrale
        
        Raisonnement:
        1. Analyser la grille d'entrée
        2. Identifier les transformations
        3. Appliquer les règles
        
        Interprétation:
        La grille subit une transformation complexe avec rotation et changement de couleurs.
        
        Solution proposée:
        [[1,2,3],[4,5,6],[7,8,9]]
        """
        
        result = self.parser.parse_ai_response(response)
        
        self.assertIsInstance(result, ParsedResponse)
        self.assertGreaterEqual(len(result.transformations), 2)
        self.assertTrue(any("Rotation de 90 degrés" in t for t in result.transformations))
        self.assertGreaterEqual(len(result.patterns), 2)
        self.assertTrue(any("Symétrie horizontale" in p for p in result.patterns))
        self.assertGreaterEqual(len(result.rules), 2)
        self.assertTrue(any("Si couleur bleue alors rotation" in r for r in result.rules))
        self.assertGreaterEqual(len(result.reasoning_steps), 3)
        self.assertIn("La grille subit une transformation", result.interpretation)
        self.assertIsNotNone(result.proposed_grid)
        self.assertEqual(result.raw_response, response)
    
    def test_extract_solution_grid_bracket_format(self):
        """Test d'extraction de grille au format crochets."""
        response = "La solution est: [[1,2,3],[4,5,6],[7,8,9]]"
        
        grid = self.parser.extract_solution_grid(response)
        
        self.assertIsNotNone(grid)
        expected = np.array([[1,2,3],[4,5,6],[7,8,9]])
        np.testing.assert_array_equal(grid, expected)
    
    def test_extract_solution_grid_multiple_formats(self):
        """Test d'extraction avec plusieurs formats de grille."""
        test_cases = [
            ("Solution: [[0,1],[2,3]]", np.array([[0,1],[2,3]])),
            ("Grille: 1,2,3,4", None),  # Format numérique simple - peut échouer
            ("Résultat: [[8,9],[0,1]]", np.array([[8,9],[0,1]])),  # Format structuré
        ]
        
        for response, expected in test_cases:
            with self.subTest(response=response):
                grid = self.parser.extract_solution_grid(response)
                if expected is None:
                    # Certains formats peuvent échouer, c'est acceptable
                    continue
                elif grid is not None and expected.size > 0:
                    # Vérifier l'égalité exacte pour les formats structurés
                    np.testing.assert_array_equal(grid, expected)
    
    def test_extract_solution_grid_invalid_colors(self):
        """Test avec des couleurs invalides (hors range 0-9)."""
        response = "Solution: [[10,11,12],[13,14,15]]"
        
        grid = self.parser.extract_solution_grid(response)
        
        # Devrait retourner None car les couleurs sont invalides
        self.assertIsNone(grid)
    
    def test_extract_solution_grid_oversized(self):
        """Test avec une grille trop grande."""
        # Créer une grille 31x31 (trop grande)
        large_grid = "[[" + ",".join(["0"] * 31) + "]" * 31 + "]"
        response = f"Solution: {large_grid}"
        
        grid = self.parser.extract_solution_grid(response)
        
        # Devrait retourner None car trop grande
        self.assertIsNone(grid)
    
    def test_extract_solution_grid_no_grid(self):
        """Test sans grille dans la réponse."""
        response = "Aucune solution trouvée dans cette analyse."
        
        grid = self.parser.extract_solution_grid(response)
        
        self.assertIsNone(grid)
    
    def test_parse_bracket_format(self):
        """Test du parsing au format crochets."""
        grid_text = "[[1,2],[3,4]]"
        
        grid = self.parser._parse_bracket_format(grid_text)
        
        expected = np.array([[1,2],[3,4]])
        np.testing.assert_array_equal(grid, expected)
    
    def test_parse_json_format(self):
        """Test du parsing au format JSON."""
        grid_text = '[[0,1,2],[3,4,5]]'
        
        grid = self.parser._parse_json_format(grid_text)
        
        expected = np.array([[0,1,2],[3,4,5]])
        np.testing.assert_array_equal(grid, expected)
    
    def test_parse_numeric_format(self):
        """Test du parsing au format numérique."""
        grid_text = "1 2 3 4"
        
        grid = self.parser._parse_numeric_format(grid_text)
        
        # Devrait créer une grille 2x2 ou 1x4
        self.assertIsNotNone(grid)
        self.assertTrue(np.array_equal(grid.flatten(), [1,2,3,4]))
    
    def test_parse_separated_format(self):
        """Test du parsing avec séparateurs."""
        grid_text = """
        1,2,3
        4,5,6
        7,8,9
        """
        
        grid = self.parser._parse_separated_format(grid_text)
        
        expected = np.array([[1,2,3],[4,5,6],[7,8,9]])
        np.testing.assert_array_equal(grid, expected)
    
    def test_validate_grid_valid(self):
        """Test de validation avec une grille valide."""
        valid_grid = np.array([[0,1,2],[3,4,5]])
        
        result = self.parser._validate_grid(valid_grid)
        
        self.assertTrue(result)
    
    def test_validate_grid_invalid_dimensions(self):
        """Test de validation avec dimensions invalides."""
        # Grille 1D
        invalid_1d = np.array([1,2,3])
        self.assertFalse(self.parser._validate_grid(invalid_1d))
        
        # Grille 3D
        invalid_3d = np.array([[[1,2],[3,4]]])
        self.assertFalse(self.parser._validate_grid(invalid_3d))
        
        # Grille vide
        empty_grid = np.array([])
        self.assertFalse(self.parser._validate_grid(empty_grid))
    
    def test_validate_grid_invalid_colors(self):
        """Test de validation avec couleurs invalides."""
        invalid_colors = np.array([[10,11],[12,13]])
        
        result = self.parser._validate_grid(invalid_colors)
        
        self.assertFalse(result)
    
    def test_extract_transformations(self):
        """Test d'extraction des transformations."""
        response = """
        Transformations détectées:
        - Rotation 90°
        - Miroir horizontal
        - Changement couleur
        """
        
        transformations = self.parser._extract_transformations(response)
        
        self.assertEqual(len(transformations), 3)
        self.assertIn("Rotation 90°", transformations)
        self.assertIn("Miroir horizontal", transformations)
    
    def test_extract_patterns(self):
        """Test d'extraction des patterns."""
        response = """
        Patterns identifiés:
        • Symétrie centrale
        • Répétition de motifs
        • Alignement vertical
        """
        
        patterns = self.parser._extract_patterns(response)
        
        self.assertEqual(len(patterns), 3)
        self.assertIn("Symétrie centrale", patterns)
    
    def test_extract_rules(self):
        """Test d'extraction des règles."""
        response = """
        Règles déduites:
        1. Si bleu alors rotation
        2. Maintenir les bordures
        3. Préserver la symétrie
        """
        
        rules = self.parser._extract_rules(response)
        
        self.assertEqual(len(rules), 3)
        self.assertIn("Si bleu alors rotation", rules)
    
    def test_extract_reasoning_steps(self):
        """Test d'extraction des étapes de raisonnement."""
        response = """
        Raisonnement:
        1. Analyser la grille d'entrée
        2. Identifier les transformations
        3. Appliquer les règles
        4. Générer la solution
        """
        
        steps = self.parser._extract_reasoning_steps(response)
        
        self.assertEqual(len(steps), 4)
        self.assertIn("1. Analyser la grille d'entrée", steps)
    
    def test_extract_interpretation(self):
        """Test d'extraction de l'interprétation."""
        response = """
        Interprétation:
        Cette grille présente une transformation complexe avec rotation et symétrie.
        Les couleurs suivent un pattern spécifique.
        """
        
        interpretation = self.parser._extract_interpretation(response)
        
        self.assertIn("transformation complexe", interpretation)
    
    def test_extract_list_items_various_formats(self):
        """Test d'extraction d'éléments de liste avec différents formats."""
        test_cases = [
            ("- Item 1\n- Item 2", ["Item 1", "Item 2"]),
            ("* Point A\n* Point B", ["Point A", "Point B"]),
            ("1. First\n2. Second", ["First", "Second"]),
            ("a) Alpha\nb) Beta", ["Alpha", "Beta"]),
        ]
        
        patterns = [r'(?i)test\s*:?\s*(.*?)$']
        
        for list_text, expected in test_cases:
            response = f"Test:\n{list_text}"
            with self.subTest(list_text=list_text):
                items = self.parser._extract_list_items(response, patterns)
                # Vérifier que nous avons au moins autant d'éléments que prévu
                self.assertGreaterEqual(len(items), len(expected))
                # Vérifier que les éléments attendus sont présents
                for expected_item in expected:
                    found = any(expected_item in item for item in items)
                    if not found:
                        print(f"Expected '{expected_item}' not found in {items}")
                    self.assertTrue(found, f"Expected '{expected_item}' not found in {items}")
    
    def test_malformed_response_handling(self):
        """Test de gestion des réponses malformées."""
        malformed_responses = [
            "[[1,2,3],[4,5,incomplete",  # JSON incomplet
            "Solution: invalid_format",   # Format invalide
            "Grille: [[a,b,c]]",         # Caractères non numériques
            "[[1,2],[3,4,5,6]]",         # Lignes de tailles différentes
        ]
        
        for response in malformed_responses:
            with self.subTest(response=response):
                # Ne devrait pas lever d'exception
                result = self.parser.parse_ai_response(f"Analyse: {response}")
                self.assertIsInstance(result, ParsedResponse)
                # La grille peut être None si malformée
                if result.proposed_grid is not None:
                    self.assertTrue(self.parser._validate_grid(result.proposed_grid))
    
    def test_response_too_long(self):
        """Test avec une réponse trop longue."""
        # Créer une réponse très longue
        long_response = "Analyse: " + "x" * 60000
        
        with patch('scripts.core.response_parser.SystemLimits.MAX_RESPONSE_LENGTH', 1000):
            result = self.parser.parse_ai_response(long_response)
            
            # Devrait être tronquée
            self.assertLessEqual(len(result.raw_response), 1000)
    
    def test_edge_cases_grid_extraction(self):
        """Test des cas limites pour l'extraction de grille."""
        edge_cases = [
            ("[[0]]", np.array([[0]])),  # Grille 1x1
            ("[[0,1,2,3,4,5,6,7,8,9]]", np.array([[0,1,2,3,4,5,6,7,8,9]])),  # Toutes les couleurs
            ("Solution: [[]]", None),  # Grille vide
            ("Multiple: [[1,2]] and [[3,4]]", np.array([[3,4]])),  # Dernière grille
        ]
        
        for response, expected in edge_cases:
            with self.subTest(response=response):
                grid = self.parser.extract_solution_grid(response)
                if expected is None:
                    self.assertIsNone(grid)
                else:
                    self.assertIsNotNone(grid)
                    np.testing.assert_array_equal(grid, expected)
    
    def test_multilingual_support(self):
        """Test du support multilingue."""
        multilingual_response = """
        Transformations detected:
        - Rotation
        
        Motifs identifiés:
        - Symétrie
        
        Règles déduites:
        - Preserve structure
        
        Reasoning étapes:
        1. Analyze input
        
        Interpretation analyse:
        Mixed language response with solution [[1,0],[0,1]]
        """
        
        result = self.parser.parse_ai_response(multilingual_response)
        
        self.assertGreater(len(result.transformations), 0)
        self.assertGreater(len(result.patterns), 0)
        self.assertGreater(len(result.rules), 0)
        self.assertIsNotNone(result.proposed_grid)


class TestResponseParserIntegration(unittest.TestCase):
    """Tests d'intégration pour ResponseParser."""
    
    def setUp(self):
        """Configuration avant chaque test."""
        self.parser = ResponseParser()
    
    def test_realistic_ai_response(self):
        """Test avec une réponse IA réaliste complète."""
        realistic_response = """
        Analyse du puzzle ARC:
        
        Transformations détectées:
        - Rotation de 90 degrés dans le sens horaire
        - Changement de couleur: 1→2, 3→4
        - Déplacement spatial des objets
        
        Patterns identifiés:
        - Les objets bleus deviennent rouges
        - La structure générale est préservée
        - Symétrie par rapport au centre
        
        Règles logiques déduites:
        - Si un pixel est bleu (1), il devient rouge (2)
        - Si un pixel est vert (3), il devient jaune (4)
        - Les pixels noirs (0) restent inchangés
        - La rotation s'applique à l'ensemble
        
        Raisonnement étape par étape:
        1. Identifier les couleurs présentes dans la grille d'entrée
        2. Appliquer les transformations de couleur selon les règles
        3. Effectuer la rotation de 90 degrés
        4. Vérifier la cohérence du résultat
        
        Interprétation de la grille test:
        La grille d'entrée contient un motif avec des pixels bleus et verts.
        En appliquant les règles de transformation, nous obtenons le résultat suivant.
        
        Grille de solution proposée:
        [[2,0,4],
         [0,2,0],
         [4,0,2]]
        """
        
        result = self.parser.parse_ai_response(realistic_response)
        
        # Vérifier que tous les éléments sont extraits
        self.assertGreaterEqual(len(result.transformations), 3)
        self.assertGreaterEqual(len(result.patterns), 3)
        self.assertGreaterEqual(len(result.rules), 4)
        self.assertGreaterEqual(len(result.reasoning_steps), 4)
        self.assertIn("grille d'entrée", result.interpretation.lower())
        
        # Vérifier la grille
        self.assertIsNotNone(result.proposed_grid)
        expected_grid = np.array([[2,0,4],[0,2,0],[4,0,2]])
        np.testing.assert_array_equal(result.proposed_grid, expected_grid)
        
        # Vérifier que la réponse brute est préservée
        self.assertEqual(result.raw_response, realistic_response)


if __name__ == '__main__':
    # Configuration du logging pour les tests
    import logging
    logging.basicConfig(level=logging.WARNING)
    
    # Exécuter les tests
    unittest.main(verbosity=2)