#!/usr/bin/env python3
"""
Test complet du workflow de génération de prompts améliorés
"""

import os
import subprocess
import tempfile
import shutil
from pathlib import Path

def test_auto_analysis_generation():
    """Teste la génération automatique d'analyse"""
    print("🧪 Test 1: Génération automatique d'analyse")
    
    # Choisir un puzzle qui a une analyse existante
    test_taskid = "2204b7a8"
    analysis_file = f"analysis_data/training/{test_taskid}_analysis.json"
    
    # Supprimer l'analyse si elle existe (pour le test)
    if os.path.exists(analysis_file):
        os.remove(analysis_file)
        print(f"🗑️  Analyse supprimée pour le test: {analysis_file}")
    
    # Tester la génération automatique
    cmd = ["python", "scripts/core/generate_enhanced_prompt.py", 
           "--taskid", test_taskid, 
           "--arc-data-dir", "../arc-puzzle/arcdata",
           "--analysis-dir", "analysis_data",
           "--output-dir", "arc_results",
           "--quiet"]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        # Vérifier que l'analyse a été créée
        if os.path.exists(analysis_file):
            print("✅ Génération automatique d'analyse: RÉUSSIE")
            return True
        else:
            print("❌ Génération automatique d'analyse: ÉCHEC - fichier non créé")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Génération automatique d'analyse: ÉCHEC - {e}")
        print(f"Sortie d'erreur: {e.stderr}")
        return False

def test_force_regeneration():
    """Teste la régénération forcée"""
    print("\n🧪 Test 2: Régénération forcée")
    
    test_taskid = "2204b7a8"
    analysis_file = f"analysis_data/training/{test_taskid}_analysis.json"
    
    # S'assurer que l'analyse existe
    if not os.path.exists(analysis_file):
        subprocess.run(["python", "generate_analysis.py", "--taskid", test_taskid], 
                      capture_output=True)
    
    # Obtenir la date de modification originale
    original_mtime = os.path.getmtime(analysis_file) if os.path.exists(analysis_file) else 0
    
    # Attendre un peu pour s'assurer que la nouvelle date sera différente
    import time
    time.sleep(1)
    
    # Tester la régénération forcée
    cmd = ["python", "scripts/core/generate_enhanced_prompt.py", 
           "--taskid", test_taskid, 
           "--arc-data-dir", "../arc-puzzle/arcdata",
           "--analysis-dir", "analysis_data",
           "--output-dir", "arc_results",
           "--force-regenerate", "--quiet"]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        # Vérifier que le fichier a été régénéré
        new_mtime = os.path.getmtime(analysis_file)
        
        if new_mtime > original_mtime:
            print("✅ Régénération forcée: RÉUSSIE")
            return True
        else:
            print("❌ Régénération forcée: ÉCHEC - fichier non mis à jour")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Régénération forcée: ÉCHEC - {e}")
        return False

def test_insights_only_mode():
    """Teste le mode insights seulement"""
    print("\n🧪 Test 3: Mode insights seulement")
    
    test_taskid = "2204b7a8"
    
    cmd = ["python", "scripts/core/generate_enhanced_prompt.py", 
           "--taskid", test_taskid, 
           "--arc-data-dir", "../arc-puzzle/arcdata",
           "--analysis-dir", "analysis_data",
           "--output-dir", "arc_results",
           "--show-insights-only"]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        # Vérifier que la sortie contient des insights
        if "INSIGHTS CRITIQUES" in result.stdout:
            print("✅ Mode insights seulement: RÉUSSI")
            return True
        else:
            print("❌ Mode insights seulement: ÉCHEC - pas d'insights dans la sortie")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Mode insights seulement: ÉCHEC - {e}")
        return False

def test_generic_validation():
    """Teste la validation de généricité"""
    print("\n🧪 Test 4: Validation de généricité")
    
    cmd = ["python", "scripts/utils/validate_generic_insights.py"]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        if "VALIDATION RÉUSSIE" in result.stdout:
            print("✅ Validation de généricité: RÉUSSIE")
            return True
        else:
            print("❌ Validation de généricité: ÉCHEC")
            print(result.stdout)
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Validation de généricité: ÉCHEC - {e}")
        print(f"Sortie: {e.stdout}")
        return False

def test_complete_workflow():
    """Teste le workflow complet"""
    print("\n🧪 Test 5: Workflow complet")
    
    test_taskid = "2204b7a8"
    
    # Supprimer les fichiers de sortie s'ils existent
    analysis_file = f"analysis_data/training/{test_taskid}_analysis.json"
    prompt_file = f"arc_results/training/{test_taskid}_prompt.txt"
    
    for file in [analysis_file, prompt_file]:
        if os.path.exists(file):
            os.remove(file)
    
    # Exécuter le workflow complet
    cmd = ["python", "scripts/core/generate_enhanced_prompt.py", 
           "--taskid", test_taskid,
           "--arc-data-dir", "../arc-puzzle/arcdata",
           "--analysis-dir", "analysis_data",
           "--output-dir", "arc_results"]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        # Vérifier que tous les fichiers ont été créés
        files_created = []
        for file in [analysis_file, prompt_file]:
            if os.path.exists(file):
                files_created.append(file)
        
        if len(files_created) == 2:
            print("✅ Workflow complet: RÉUSSI")
            print(f"   Fichiers créés: {len(files_created)}/2")
            return True
        else:
            print(f"❌ Workflow complet: ÉCHEC - {len(files_created)}/2 fichiers créés")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Workflow complet: ÉCHEC - {e}")
        return False

def main():
    """Fonction principale de test"""
    
    print("🚀 TEST COMPLET DU WORKFLOW DE GÉNÉRATION DE PROMPTS AMÉLIORÉS")
    print("=" * 70)
    
    tests = [
        test_auto_analysis_generation,
        test_force_regeneration,
        test_insights_only_mode,
        test_generic_validation,
        test_complete_workflow
    ]
    
    results = []
    
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ Erreur inattendue dans {test_func.__name__}: {e}")
            results.append(False)
    
    # Résumé des résultats
    print("\n" + "=" * 70)
    print("📋 RÉSUMÉ DES TESTS:")
    
    test_names = [
        "Génération automatique d'analyse",
        "Régénération forcée",
        "Mode insights seulement",
        "Validation de généricité",
        "Workflow complet"
    ]
    
    passed = 0
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
        print(f"{i+1}. {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 RÉSULTAT GLOBAL: {passed}/{len(tests)} tests réussis")
    
    if passed == len(tests):
        print("🎉 TOUS LES TESTS SONT RÉUSSIS - Le système est opérationnel !")
        return 0
    else:
        print("⚠️  CERTAINS TESTS ONT ÉCHOUÉ - Vérifications nécessaires")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())