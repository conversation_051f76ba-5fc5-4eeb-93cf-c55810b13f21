---
inclusion: always
priority: critical
---

# 🛡️ RÈGLES CRITIQUES - Généricité des Insights ARC AGI

## ⚠️ PRINCIPE FONDAMENTAL - ZÉRO CONTAMINATION

**RÈGLE ABSOLUE** : Les insights extraits des puzzles ARC AGI doivent être **100% génériques** et basés **uniquement** sur l'analyse automatique de chaque puzzle individuel.

### 🚫 INTERDICTIONS STRICTES

#### Informations Hardcodées INTERDITES :
- ❌ **Valeurs numériques spécifiques** : "taille ≥10", "distance Manhattan", "position (x,y)"
- ❌ **Termes spécifiques** : "bordures", "proximité aux bordures", "déplacement vers le bas"
- ❌ **Règles prédéfinies** : "distance Manhattan", "gravité", "glissement"
- ❌ **Conseils spécifiques** : "chercher les bordures fixes", "éviter déplacement vers le bas"
- ❌ **Références à d'autres puzzles** : "comme dans le puzzle X", "similaire à..."

#### Contamination Croisée INTERDITE :
- ❌ **Réutiliser des insights** d'un puzzle pour un autre
- ❌ **Copier des observations** entre différents puzzles
- ❌ **Appliquer des patterns** découverts sur un puzzle à un autre

### ✅ APPROCHE AUTORISÉE

#### Extraction Générique Basée sur les Données :
- ✅ **Distribution de tailles** : "taille 1 (14x), taille 10 (6x)" (basé sur les données)
- ✅ **Patterns d'alignement** : "horizontal", "vertical", "mixed" (détectés automatiquement)
- ✅ **Transformations détectées** : "color_removal", "in_place_transformation" (de l'analyse)
- ✅ **Statistiques réelles** : "4.7% des pixels modifiés" (calculé automatiquement)
- ✅ **Couleurs transformées** : "3→4: 3 occurrences" (extrait des données)

#### Conseils Conditionnels Autorisés :
- ✅ **SI** transformation de couleur détectée **ALORS** "se concentrer sur les couleurs"
- ✅ **SI** faible taux de changement **ALORS** "chercher transformations locales"
- ✅ **SI** objets de tailles variées **ALORS** "analyser relations entre tailles"

### 🔍 VALIDATION OBLIGATOIRE

#### Avant Chaque Commit/Release :
```bash
# OBLIGATOIRE : Valider la généricité
python validate_generic_insights.py

# OBLIGATOIRE : Tester sur multiple puzzles
python test_enhanced_prompt.py
```

#### Critères de Validation :
- ✅ **Aucun mot-clé interdit** dans les insights
- ✅ **Insights différents** entre puzzles différents
- ✅ **Basé uniquement** sur l'analyse automatique
- ✅ **Pas de références croisées** entre puzzles

### 🚨 CONSÉQUENCES DE LA VIOLATION

**IMPACT CATASTROPHIQUE** si ces règles sont violées :
- 🔥 **Contamination croisée** : Un puzzle récupère des infos d'un autre
- 🔥 **Fausses pistes** : Insights incorrects induisent en erreur
- 🔥 **Perte de confiance** : Le système devient non-fiable
- 🔥 **Échec de résolution** : Mauvaises hypothèses de départ

### 📋 CHECKLIST OBLIGATOIRE

Avant d'utiliser ou modifier le système d'insights :

- [ ] **Vérifier** : Aucune valeur hardcodée dans le code
- [ ] **Tester** : `validate_generic_insights.py` passe avec succès
- [ ] **Confirmer** : Les insights sont différents entre puzzles
- [ ] **Valider** : Toutes les informations proviennent de `generate_analysis.py`
- [ ] **S'assurer** : Aucune référence à des puzzles spécifiques

### 🛠️ DÉVELOPPEMENT SÉCURISÉ

#### Lors de l'ajout de nouveaux insights :
1. **TOUJOURS** baser sur les données d'analyse automatique
2. **JAMAIS** hardcoder de valeurs ou règles spécifiques
3. **SYSTÉMATIQUEMENT** tester sur multiple puzzles
4. **OBLIGATOIREMENT** valider avec le script de validation

#### Structure de Code Sécurisée :
```python
# ✅ CORRECT - Basé sur les données
if analysis_data["raw_analysis"]["transformations"]["color"]:
    insights.append("Transformation de couleur détectée")

# ❌ INCORRECT - Hardcodé
insights.append("Règle de proximité aux bordures")
```

### 🎯 OBJECTIF FINAL

**Créer un système d'insights qui :**
- 🎯 **Aide réellement** à résoudre chaque puzzle
- 🛡️ **Ne contamine jamais** un puzzle avec les infos d'un autre
- 🔍 **Reste générique** et applicable à tous les puzzles ARC AGI
- 📊 **Se base uniquement** sur l'analyse automatique objective

---

**RAPPEL CRITIQUE** : Un seul insight contaminé peut ruiner la résolution de multiples puzzles. La généricité n'est pas optionnelle, elle est **VITALE** pour le succès du système.