# Configuration des clés API pour ARC AGI Solver
# Copiez ce fichier vers .env et remplissez vos clés API

# OpenAI API Key
# Obtenez votre clé sur: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Groq API Key
# Obtenez votre clé sur: https://api.groq.com/openai/v1
GROQ_API_KEY=your_groq_api_key_here

# OpenRouter API Key  
# Obtenez votre clé sur: https://openrouter.ai/keys
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Hugging Face API Key
# Obtenez votre clé sur: https://huggingface.co/settings/tokens
HUGGINGFACE_API_KEY=your_huggingface_api_key_here

# Configuration optionnelle des modèles par défaut
# Décommentez et modifiez selon vos préférences

# OPENAI_DEFAULT_MODEL=gpt-4o-mini
# GROQ_DEFAULT_MODEL=moonshotai/kimi-k2-instruct
# OPENROUTER_DEFAULT_MODEL=anthropic/claude-3.5-sonnet
# HUGGINGFACE_DEFAULT_MODEL=microsoft/DialoGPT-large
# OLLAMA_DEFAULT_MODEL=qwen3-arc:latest

# Configuration Ollama (si différent des valeurs par défaut)
# OLLAMA_BASE_URL=http://localhost:11434