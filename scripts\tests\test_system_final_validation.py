#!/usr/bin/env python3
"""
Test de validation finale du système complet
Valide les aspects critiques du système automatisé ARC
"""

import unittest
import tempfile
import json
import numpy as np
from pathlib import Path
from unittest.mock import patch, MagicMock

from scripts.core.automated_analyzer import AutomatedARCAnalyzer
from scripts.core.exceptions import PuzzleLoadingError


class TestSystemFinalValidation(unittest.TestCase):
    """Test de validation finale du système complet"""
    
    def setUp(self):
        """Configuration des tests"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = {
            'arc_data_dir': self.temp_dir,
            'analysis_dir': f"{self.temp_dir}/analysis",
            'results_dir': f"{self.temp_dir}/results",
            'chat_session_type': 'blind_analysis',
            'enable_learning': False,  # Désactiver pour simplifier
            'save_success': True
        }
        
        # Créer la structure complète
        Path(f"{self.temp_dir}/training").mkdir(parents=True, exist_ok=True)
        Path(f"{self.temp_dir}/evaluation").mkdir(parents=True, exist_ok=True)
        Path(self.config['analysis_dir']).mkdir(parents=True, exist_ok=True)
        Path(self.config['results_dir']).mkdir(parents=True, exist_ok=True)
        
        # Créer un puzzle de test simple
        self.test_puzzle = {
            "train": [
                {
                    "input": [[1, 0], [0, 1]],
                    "output": [[2, 0], [0, 2]]
                }
            ],
            "test": [
                {
                    "input": [[1, 1], [0, 0]],
                    "output": [[2, 2], [0, 0]]
                }
            ]
        }
        
        # Sauvegarder le puzzle
        with open(f"{self.temp_dir}/training/simple_001.json", 'w') as f:
            json.dump(self.test_puzzle, f)
    
    def test_complete_workflow_validation(self):
        """Test de validation du workflow complet"""
        with patch('scripts.core.chat_session.ChatSession') as mock_chat_class:
            mock_chat = MagicMock()
            mock_chat_class.return_value = mock_chat
            
            # Réponse IA qui correspond exactement aux dimensions attendues
            mock_chat.send_prompt.return_value = """
            TRANSFORMATIONS DÉTECTÉES:
            - Remplacement de couleur: 1 → 2
            
            PATTERNS IDENTIFIÉS:
            - Conservation de la structure
            
            RÈGLES DÉDUITES:
            - Si input[i][j] = 1 alors output[i][j] = 2
            
            GRILLE OUTPUT PROPOSÉE:
            2 2
            0 0
            """
            
            analyzer = AutomatedARCAnalyzer(self.config)
            result = analyzer.analyze_puzzle("simple_001")
            
            # Vérifications essentielles
            self.assertIsNotNone(result)
            self.assertEqual(result.puzzle_id, "simple_001")
            self.assertTrue(result.success)
            self.assertIsNotNone(result.proposed_solution)
            self.assertTrue(result.validation_result.is_correct)
            self.assertEqual(result.validation_result.accuracy_percentage, 100.0)
    
    def test_error_handling_validation(self):
        """Test de validation de la gestion d'erreurs"""
        analyzer = AutomatedARCAnalyzer(self.config)
        
        # Test avec puzzle inexistant - devrait retourner un résultat d'échec
        result = analyzer.analyze_puzzle("nonexistent_puzzle")
        self.assertIsNotNone(result)
        self.assertFalse(result.success)
        self.assertEqual(result.puzzle_id, "nonexistent_puzzle")
    
    def test_output_format_validation(self):
        """Test de validation du format de sortie"""
        with patch('scripts.core.chat_session.ChatSession') as mock_chat_class:
            mock_chat = MagicMock()
            mock_chat_class.return_value = mock_chat
            mock_chat.send_prompt.return_value = """
            TRANSFORMATIONS DÉTECTÉES:
            - Test transformation
            
            PATTERNS IDENTIFIÉS:
            - Test pattern
            
            RÈGLES DÉDUITES:
            - Test rule
            
            GRILLE OUTPUT PROPOSÉE:
            2 2
            0 0
            """
            
            analyzer = AutomatedARCAnalyzer(self.config)
            result = analyzer.analyze_puzzle("simple_001")
            
            # Vérifier que le résultat est bien formaté
            self.assertTrue(result.success)
            
            # Vérifier que le fichier de résultat est créé
            expected_file = Path(self.config['results_dir']) / "puzzle_simple_001_success.txt"
            self.assertTrue(expected_file.exists())
            
            # Vérifier le contenu du fichier
            content = expected_file.read_text(encoding='utf-8')
            required_sections = [
                "=== ANALYSE PUZZLE",
                "📊 MÉTADONNÉES",
                "🔍 TRANSFORMATIONS DÉTECTÉES",
                "🎯 PATTERNS IDENTIFIÉS",
                "📋 RÈGLES DÉDUITES",
                "✅ VALIDATION"
            ]
            
            for section in required_sections:
                self.assertIn(section, content)
    
    def test_learning_system_isolation(self):
        """Test de validation de l'isolation du système d'apprentissage"""
        # Configuration avec apprentissage activé
        config_with_learning = self.config.copy()
        config_with_learning['enable_learning'] = True
        
        with patch('scripts.core.chat_session.ChatSession') as mock_chat_class:
            mock_chat = MagicMock()
            mock_chat_class.return_value = mock_chat
            mock_chat.send_prompt.return_value = """
            TRANSFORMATIONS DÉTECTÉES:
            - Remplacement spécifique
            
            PATTERNS IDENTIFIÉS:
            - Pattern unique
            
            RÈGLES DÉDUITES:
            - Règle spécifique
            
            GRILLE OUTPUT PROPOSÉE:
            2 2
            0 0
            """
            
            # Créer un deuxième puzzle
            puzzle2 = {
                "train": [{"input": [[3, 0]], "output": [[4, 0]]}],
                "test": [{"input": [[3, 3]], "output": [[4, 4]]}]
            }
            
            with open(f"{self.temp_dir}/training/simple_002.json", 'w') as f:
                json.dump(puzzle2, f)
            
            analyzer = AutomatedARCAnalyzer(config_with_learning)
            
            # Analyser les deux puzzles
            result1 = analyzer.analyze_puzzle("simple_001")
            result2 = analyzer.analyze_puzzle("simple_002")
            
            # Vérifier que les analyses sont indépendantes
            self.assertIsNotNone(result1)
            self.assertIsNotNone(result2)
            self.assertEqual(result1.puzzle_id, "simple_001")
            self.assertEqual(result2.puzzle_id, "simple_002")
    
    def test_performance_validation(self):
        """Test de validation des performances"""
        # Créer plusieurs puzzles simples
        for i in range(3, 6):
            puzzle = {
                "train": [{"input": [[i]], "output": [[i+1]]}],
                "test": [{"input": [[i]], "output": [[i+1]]}]
            }
            with open(f"{self.temp_dir}/training/perf_00{i}.json", 'w') as f:
                json.dump(puzzle, f)
        
        with patch('scripts.core.chat_session.ChatSession') as mock_chat_class:
            mock_chat = MagicMock()
            mock_chat_class.return_value = mock_chat
            mock_chat.send_prompt.return_value = """
            TRANSFORMATIONS: Incrémentation
            PATTERNS: Séquentiel
            RÈGLES: +1
            GRILLE OUTPUT PROPOSÉE:
            4
            """
            
            analyzer = AutomatedARCAnalyzer(self.config)
            results = []
            
            # Analyser tous les puzzles
            for i in range(3, 6):
                result = analyzer.analyze_puzzle(f"perf_00{i}")
                results.append(result)
            
            # Vérifier que tous ont été traités
            self.assertEqual(len(results), 3)
            for result in results:
                self.assertIsNotNone(result)
                self.assertGreater(result.execution_time, 0)
    
    def test_cli_integration_validation(self):
        """Test de validation de l'intégration CLI"""
        # Ce test vérifie que l'interface CLI fonctionne correctement
        from arc_enhanced_prompt import main
        import sys
        from io import StringIO
        
        # Capturer la sortie
        old_stdout = sys.stdout
        sys.stdout = captured_output = StringIO()
        
        try:
            # Tester l'aide
            sys.argv = ['arc_enhanced_prompt.py', 'analyze', '--help']
            try:
                main()
            except SystemExit:
                pass  # L'aide provoque un SystemExit normal
            
            output = captured_output.getvalue()
            self.assertIn("ID du puzzle à analyser", output)
            
        finally:
            sys.stdout = old_stdout


class TestSystemRobustness(unittest.TestCase):
    """Tests de robustesse du système"""
    
    def setUp(self):
        """Configuration des tests de robustesse"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = {
            'arc_data_dir': self.temp_dir,
            'analysis_dir': f"{self.temp_dir}/analysis",
            'results_dir': f"{self.temp_dir}/results",
            'chat_session_type': 'blind_analysis',
            'enable_learning': False,
            'save_success': True
        }
        
        # Créer la structure
        Path(f"{self.temp_dir}/training").mkdir(parents=True, exist_ok=True)
        Path(f"{self.temp_dir}/evaluation").mkdir(parents=True, exist_ok=True)
        Path(self.config['analysis_dir']).mkdir(parents=True, exist_ok=True)
        Path(self.config['results_dir']).mkdir(parents=True, exist_ok=True)
    
    def test_malformed_puzzle_handling(self):
        """Test de gestion des puzzles malformés"""
        # Créer un puzzle malformé
        malformed_puzzle = {"invalid": "structure"}
        
        with open(f"{self.temp_dir}/training/malformed_001.json", 'w') as f:
            json.dump(malformed_puzzle, f)
        
        analyzer = AutomatedARCAnalyzer(self.config)
        result = analyzer.analyze_puzzle("malformed_001")
        
        # Devrait gérer l'erreur gracieusement
        self.assertIsNotNone(result)
        self.assertFalse(result.success)
    
    def test_invalid_ai_response_handling(self):
        """Test de gestion des réponses IA invalides"""
        # Créer un puzzle valide
        valid_puzzle = {
            "train": [{"input": [[1]], "output": [[2]]}],
            "test": [{"input": [[1]], "output": [[2]]}]
        }
        
        with open(f"{self.temp_dir}/training/valid_001.json", 'w') as f:
            json.dump(valid_puzzle, f)
        
        with patch('scripts.core.chat_session.ChatSession') as mock_chat_class:
            mock_chat = MagicMock()
            mock_chat_class.return_value = mock_chat
            
            # Réponse IA invalide/vide
            mock_chat.send_prompt.return_value = "Réponse invalide sans structure"
            
            analyzer = AutomatedARCAnalyzer(self.config)
            result = analyzer.analyze_puzzle("valid_001")
            
            # Devrait gérer l'erreur gracieusement
            self.assertIsNotNone(result)
            self.assertFalse(result.success)


if __name__ == '__main__':
    unittest.main()